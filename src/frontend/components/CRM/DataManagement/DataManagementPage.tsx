import React, { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "react-query";
import { CheckCircleIcon } from "@heroicons/react/24/solid";
import {
  getCompaniesWithLinkingStatus,
  linkCompanyToHubSpot,
  linkCompanyToHarvest,
  unlinkCompanyFromHubSpot,
  unlinkCompanyFromHarvest,
} from "../../../api/crm";
import { getHarvestCompanies } from "../../../api/leads";
import { getHubSpotCompaniesForLinking } from "../../../api/hubspot";
import { Card } from "../../shared/Card";
import { Button } from "../../shared/Button";
import { Badge } from "../../shared/Badge";
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableCell,
} from "../../shared/lists/Table";
import LinkedStatus from "../../shared/LinkedStatus";
import LinkedStatusCompact from "./LinkedStatusCompact";
import LinkingModal from "./LinkingModal";
import type { Company } from "../../../types/crm-types";

interface LinkingState {
  isOpen: boolean;
  companyId: string | null;
  companyName: string | null;
  linkType: "hubspot" | "harvest" | null;
}

/**
 * Data Management page for managing company links between systems
 */
const DataManagementPage: React.FC = () => {
  const [linkingState, setLinkingState] = useState<LinkingState>({
    isOpen: false,
    companyId: null,
    companyName: null,
    linkType: null,
  });

  const queryClient = useQueryClient();

  // Fetch all companies with linking status
  const {
    data: companiesWithStatus = [],
    isLoading: isLoadingCompanies,
    error,
  } = useQuery("companiesWithLinkingStatus", getCompaniesWithLinkingStatus, {
    staleTime: 300000, // 5 minutes
    refetchOnWindowFocus: false,
    onError: (error) => {
      console.error("Error fetching companies with linking status:", error);
    },
    onSuccess: (data) => {
      console.log("Companies with linking status:", data);
    },
  });

  // Fetch Harvest companies for linking options
  const { data: harvestCompanies = [] } = useQuery(
    "harvestCompanies",
    getHarvestCompanies,
    {
      staleTime: 300000,
      refetchOnWindowFocus: false,
    }
  );

  // Fetch HubSpot companies for linking options
  const { data: hubspotCompanies = [] } = useQuery(
    "hubspotCompaniesForLinking",
    getHubSpotCompaniesForLinking,
    {
      staleTime: 300000,
      refetchOnWindowFocus: false,
      enabled: linkingState.isOpen && linkingState.linkType === "hubspot", // Only fetch when modal is open for HubSpot linking
    }
  );

  // Link to HubSpot mutation
  const linkToHubSpotMutation = useMutation(
    ({ companyId, hubspotId }: { companyId: string; hubspotId: string }) =>
      linkCompanyToHubSpot(companyId, hubspotId),
    {
      onSuccess: () => {
        queryClient.invalidateQueries("companiesWithLinkingStatus");
        queryClient.invalidateQueries("allCompanies");
        setLinkingState({
          isOpen: false,
          companyId: null,
          companyName: null,
          linkType: null,
        });
      },
      onError: (error: any) => {
        console.error("Error linking to HubSpot:", error);
        // You could add toast notification here
      },
    }
  );

  // Link to Harvest mutation
  const linkToHarvestMutation = useMutation(
    ({ companyId, harvestId }: { companyId: string; harvestId: number }) =>
      linkCompanyToHarvest(companyId, harvestId),
    {
      onSuccess: () => {
        queryClient.invalidateQueries("companiesWithLinkingStatus");
        queryClient.invalidateQueries("allCompanies");
        setLinkingState({
          isOpen: false,
          companyId: null,
          companyName: null,
          linkType: null,
        });
      },
      onError: (error: any) => {
        console.error("Error linking to Harvest:", error);
        // You could add toast notification here
      },
    }
  );

  // Unlink mutations
  const unlinkFromHubSpotMutation = useMutation(
    (companyId: string) => unlinkCompanyFromHubSpot(companyId),
    {
      onSuccess: () => {
        queryClient.invalidateQueries("companiesWithLinkingStatus");
        queryClient.invalidateQueries("allCompanies");
      },
    }
  );

  const unlinkFromHarvestMutation = useMutation(
    (companyId: string) => unlinkCompanyFromHarvest(companyId),
    {
      onSuccess: () => {
        queryClient.invalidateQueries("companiesWithLinkingStatus");
        queryClient.invalidateQueries("allCompanies");
      },
    }
  );

  // Handle opening linking modal
  const handleLinkClick = (
    company: Company,
    linkType: "hubspot" | "harvest"
  ) => {
    setLinkingState({
      isOpen: true,
      companyId: company.id,
      companyName: company.name,
      linkType,
    });
  };

  // Handle closing linking modal
  const handleCloseModal = () => {
    setLinkingState({
      isOpen: false,
      companyId: null,
      companyName: null,
      linkType: null,
    });
  };

  // Handle linking submission
  const handleLinkSubmit = (targetId: string | number) => {
    if (!linkingState.companyId || !linkingState.linkType) return;

    if (linkingState.linkType === "hubspot") {
      linkToHubSpotMutation.mutate({
        companyId: linkingState.companyId,
        hubspotId: targetId as string,
      });
    } else {
      linkToHarvestMutation.mutate({
        companyId: linkingState.companyId,
        harvestId: targetId as number,
      });
    }
  };

  if (isLoadingCompanies) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">
            Loading companies...
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="text-red-600 dark:text-red-400 mb-4">
            <svg
              className="mx-auto h-12 w-12 mb-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Error Loading Companies
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {error instanceof Error
              ? error.message
              : "An unknown error occurred"}
          </p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
          >
            Reload Page
          </button>
        </div>
      </div>
    );
  }

  // Group companies by linking status
  const companiesByStatus = {
    both: companiesWithStatus.filter((c) => c.linkingStatus === "both"),
    hubspot_only: companiesWithStatus.filter(
      (c) => c.linkingStatus === "hubspot_only"
    ),
    harvest_only: companiesWithStatus.filter(
      (c) => c.linkingStatus === "harvest_only"
    ),
    none: companiesWithStatus.filter((c) => c.linkingStatus === "none"),
  };

  const totalCompanies = companiesWithStatus.length;
  const totalLinked = companiesByStatus.both.length;
  const totalUnlinked = totalCompanies - totalLinked;

  // Debug logging
  console.log("Companies breakdown:", {
    total: totalCompanies,
    both: companiesByStatus.both.length,
    hubspotOnly: companiesByStatus.hubspot_only.length,
    harvestOnly: companiesByStatus.harvest_only.length,
    none: companiesByStatus.none.length,
    totalLinked,
    totalUnlinked,
    rawData: companiesWithStatus,
  });

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Data Management
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-1">
          Manage connections between upstream companies and external systems
        </p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card className="p-4 stat-card total">
          <div className="text-center relative">
            <div className="text-2xl font-bold text-purple-600 dark:text-purple-400 stat-value">
              {totalCompanies}
            </div>
            <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mt-1">
              Total Companies
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-500 mt-0.5">
              All sources
            </div>
          </div>
        </Card>

        <Card className="p-4 stat-card linked">
          <div className="text-center relative">
            <div className="text-2xl font-bold text-green-600 dark:text-green-400 stat-value">
              {companiesByStatus.both.length}
            </div>
            <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mt-1">
              Fully Linked
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-500 mt-0.5">
              HubSpot + Harvest
            </div>
          </div>
        </Card>

        <Card className="p-4 stat-card hubspot">
          <div className="text-center relative">
            <div className="text-2xl font-bold text-orange-600 dark:text-orange-400 stat-value">
              {companiesByStatus.hubspot_only.length}
            </div>
            <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mt-1">
              HubSpot Only
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-500 mt-0.5">
              Need Harvest link
            </div>
          </div>
        </Card>

        <Card className="p-4 stat-card harvest">
          <div className="text-center relative">
            <div className="text-2xl font-bold text-emerald-600 dark:text-emerald-400 stat-value">
              {companiesByStatus.harvest_only.length}
            </div>
            <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mt-1">
              Harvest Only
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-500 mt-0.5">
              Need HubSpot link
            </div>
          </div>
        </Card>

        <Card className="p-4 stat-card unlinked">
          <div className="text-center relative">
            <div className="text-2xl font-bold text-red-600 dark:text-red-400 stat-value">
              {companiesByStatus.none.length}
            </div>
            <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mt-1">
              Unlinked
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-500 mt-0.5">
              Manual entries
            </div>
          </div>
        </Card>
      </div>

      {/* Companies Table */}
      <div className="space-y-4">
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            All Companies
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Manage linking between upstream companies and external systems
          </p>
        </div>

        <div className="data-management-table">
          <Table variant="default" size="default">
            <TableHeader>
              <TableRow>
                <TableCell header width="30%">
                  Company Name
                </TableCell>
                <TableCell header width="20%">
                  Status
                </TableCell>
                <TableCell header width="25%">
                  HubSpot
                </TableCell>
                <TableCell header width="25%">
                  Harvest
                </TableCell>
              </TableRow>
            </TableHeader>
            <TableBody>
              {companiesWithStatus.map((company) => {
                const getStatusBadge = (status: string) => {
                  switch (status) {
                    case "both":
                      return (
                        <Badge 
                          variant="success" 
                          className="bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300 h-7 inline-flex items-center" 
                          icon={<CheckCircleIcon className="w-3 h-3" />}
                        >
                          Fully Linked
                        </Badge>
                      );
                    case "hubspot_only":
                      return (
                        <Badge variant="info" className="bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 whitespace-nowrap h-7 inline-flex items-center">
                          HubSpot Only
                        </Badge>
                      );
                    case "harvest_only":
                      return (
                        <Badge variant="warning" className="bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300 whitespace-nowrap h-7 inline-flex items-center">
                          Harvest Only
                        </Badge>
                      );
                    case "none":
                      return (
                        <Badge variant="danger" className="bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400 whitespace-nowrap h-7 inline-flex items-center">
                          Unlinked
                        </Badge>
                      );
                    default:
                      return <Badge variant="neutral">Unknown</Badge>;
                  }
                };

                return (
                  <TableRow key={company.id}>
                    <TableCell>
                      <div className="company-cell">
                        <div className="company-name">
                          {company.name}
                        </div>
                        <div className="company-source">
                          Source: {company.source || "Manual"}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="status-cell">
                        {getStatusBadge(company.linkingStatus)}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="link-status-cell">
                        <LinkedStatusCompact
                          system="hubspot"
                          isLinked={!!company.hubspotId}
                          externalId={company.hubspotId}
                          onLink={() => handleLinkClick(company, "hubspot")}
                          onUnlink={() =>
                            unlinkFromHubSpotMutation.mutate(company.id)
                          }
                          disabled={
                            linkToHubSpotMutation.isLoading ||
                            unlinkFromHubSpotMutation.isLoading
                          }
                        />
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="link-status-cell">
                        <LinkedStatusCompact
                          system="harvest"
                          isLinked={!!company.harvestId}
                          externalId={company.harvestId}
                          onLink={() => handleLinkClick(company, "harvest")}
                          onUnlink={() =>
                            unlinkFromHarvestMutation.mutate(company.id)
                          }
                          disabled={
                            linkToHarvestMutation.isLoading ||
                            unlinkFromHarvestMutation.isLoading
                          }
                        />
                      </div>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Linking Modal */}
      <LinkingModal
        isOpen={linkingState.isOpen}
        onClose={handleCloseModal}
        onSubmit={handleLinkSubmit}
        companyName={linkingState.companyName || ""}
        linkType={linkingState.linkType}
        hubspotCompanies={hubspotCompanies.map((company) => ({
          ...company,
          hubspotId: company.id, // Map the HubSpot ID to hubspotId field expected by modal
          id: company.id, // Keep the original ID
        }))}
        harvestCompanies={harvestCompanies}
        isLoading={
          linkToHubSpotMutation.isLoading || linkToHarvestMutation.isLoading
        }
      />
    </div>
  );
};

export default DataManagementPage;
