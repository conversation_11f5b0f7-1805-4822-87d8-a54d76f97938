import React from "react";
import { FilterSummary, FILTER_RULES } from "../types/projection-audit-types";

interface FilteringRulesProps {
  summary: FilterSummary;
  showDetailedRules: boolean;
  setShowDetailedRules: (show: boolean) => void;
}

const FilteringRules: React.FC<FilteringRulesProps> = ({
  summary,
  showDetailedRules,
  setShowDetailedRules,
}) => {
  // Helper to count rule instances
  const getRuleCount = (ruleText: string): number => {
    return Object.entries(summary.byReason)
      .filter(([reason]) => reason.includes(ruleText))
      .reduce((sum, [_, count]) => sum + count, 0);
  };

  // Count occurrences for each rule
  const uninvoicedWorkCount = getRuleCount(FILTER_RULES.UNINVOICED_WORK);
  const realInvoiceCount = getRuleCount(FILTER_RULES.REAL_INVOICE);
  const paymentTermsCount = getRuleCount(FILTER_RULES.PAYMENT_TERMS);

  // Find other reasons not covered by standard rules
  const otherReasons = Object.entries(summary.byReason).filter(([reason]) => {
    return (
      reason !== FILTER_RULES.MEETS_CRITERIA &&
      !reason.includes(FILTER_RULES.UNINVOICED_WORK) &&
      !reason.includes(FILTER_RULES.REAL_INVOICE) &&
      !reason.includes(FILTER_RULES.PAYMENT_TERMS)
    );
  });

  return (
    <div className="px-4 py-3">
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center">
          <svg
            className="w-4 h-4 text-blue-logo dark:text-blue-400 mr-1.5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
            />
          </svg>
          <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Filtering Rules &amp; Exclusion Reasons
          </h3>
        </div>
        <button
          className="text-xs text-blue-logo dark:text-blue-400 hover:text-blue-logo/80 dark:hover:text-blue-300 transition px-2 py-0.5 rounded hover:bg-blue-logo/10 dark:hover:bg-blue-900/20 flex items-center"
          onClick={() => setShowDetailedRules(!showDetailedRules)}
        >
          {showDetailedRules ? (
            <>
              <svg
                className="w-3.5 h-3.5 mr-1"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M5 15l7-7 7 7"
                />
              </svg>
              Hide Details
            </>
          ) : (
            <>
              <svg
                className="w-3.5 h-3.5 mr-1"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M19 9l-7 7-7-7"
                />
              </svg>
              Show Details
            </>
          )}
        </button>
      </div>

      {/* Empty state if no exclusions */}
      {Object.entries(summary.byReason).filter(
        ([reason]) => reason !== FILTER_RULES.MEETS_CRITERIA
      ).length === 0 ? (
        <div className="flex items-center justify-center py-6 bg-gray-50 dark:bg-gray-700/30 rounded-md text-gray-500 dark:text-gray-400">
          <svg
            className="w-5 h-5 mr-2 text-gray-400 dark:text-gray-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          <span className="text-sm">
            No exclusions found in current projection data
          </span>
        </div>
      ) : showDetailedRules ? (
        // Detailed view with full explanations
        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
          {/* Rule 1: Uninvoiced Work Rule */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
            <div className="flex items-center justify-between px-3 py-2 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center">
                <div className="flex-shrink-0 w-1 h-6 bg-cyan-500 dark:bg-cyan-600 rounded mr-2"></div>
                <div className="flex items-center">
                  <div className="mr-2">
                    <div className="flex items-center justify-center w-5 h-5 rounded-full bg-cyan-100 dark:bg-cyan-900/30 text-cyan-700 dark:text-cyan-300 text-xs font-semibold">
                      1
                    </div>
                  </div>
                  <div>
                    <h4 className="text-xs font-medium text-gray-800 dark:text-gray-200">
                      Uninvoiced Work Rule
                    </h4>
                  </div>
                </div>
              </div>
              <div>
                {uninvoicedWorkCount > 0 ? (
                  <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-amber-100 dark:bg-amber-900/30 text-amber-800 dark:text-amber-300">
                    {uninvoicedWorkCount}
                  </span>
                ) : (
                  <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300">
                    0
                  </span>
                )}
              </div>
            </div>
            <div className="px-3 py-2">
              <div className="bg-cyan-50 dark:bg-cyan-900/20 p-2 rounded text-xs text-gray-800 dark:text-gray-200">
                <p>
                  <span className="font-medium">What:</span> Excludes uninvoiced
                  work when projected income exists within ±3 days
                </p>
                <p className="text-gray-600 dark:text-gray-400">
                  <span className="font-medium">Why:</span> Prevents
                  double-counting partial work
                </p>
              </div>
            </div>
          </div>

          {/* Rule 2: Real Invoice Rule */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
            <div className="flex items-center justify-between px-3 py-2 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center">
                <div className="flex-shrink-0 w-1 h-6 bg-purple-500 dark:bg-purple-600 rounded mr-2"></div>
                <div className="flex items-center">
                  <div className="mr-2">
                    <div className="flex items-center justify-center w-5 h-5 rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 text-xs font-semibold">
                      2
                    </div>
                  </div>
                  <div>
                    <h4 className="text-xs font-medium text-gray-800 dark:text-gray-200">
                      Real Invoice Rule
                    </h4>
                  </div>
                </div>
              </div>
              <div>
                {realInvoiceCount > 0 ? (
                  <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-amber-100 dark:bg-amber-900/30 text-amber-800 dark:text-amber-300">
                    {realInvoiceCount}
                  </span>
                ) : (
                  <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300">
                    0
                  </span>
                )}
              </div>
            </div>
            <div className="px-3 py-2">
              <div className="bg-purple-50 dark:bg-purple-900/20 p-2 rounded text-xs text-gray-800 dark:text-gray-200">
                <p>
                  <span className="font-medium">What:</span> Excludes projected
                  income when a real invoice exists within ±5 days
                </p>
                <p className="text-gray-600 dark:text-gray-400">
                  <span className="font-medium">Why:</span> Gives precedence to
                  actual invoices
                </p>
              </div>
            </div>
          </div>

          {/* Rule 3: Payment Terms Rule */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
            <div className="flex items-center justify-between px-3 py-2 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center">
                <div className="flex-shrink-0 w-1 h-6 bg-blue-500 dark:bg-blue-600 rounded mr-2"></div>
                <div className="flex items-center">
                  <div className="mr-2">
                    <div className="flex items-center justify-center w-5 h-5 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 text-xs font-semibold">
                      3
                    </div>
                  </div>
                  <div>
                    <h4 className="text-xs font-medium text-gray-800 dark:text-gray-200">
                      Payment Terms Rule
                    </h4>
                  </div>
                </div>
              </div>
              <div>
                {paymentTermsCount > 0 ? (
                  <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-amber-100 dark:bg-amber-900/30 text-amber-800 dark:text-amber-300">
                    {paymentTermsCount}
                  </span>
                ) : (
                  <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300">
                    0
                  </span>
                )}
              </div>
            </div>
            <div className="px-3 py-2">
              <div className="bg-blue-50 dark:bg-blue-900/20 p-2 rounded text-xs text-gray-800 dark:text-gray-200">
                <p>
                  <span className="font-medium">What:</span> Excludes projected
                  income within payment terms from today
                </p>
                <p className="text-gray-600 dark:text-gray-400">
                  <span className="font-medium">Why:</span> Income due soon
                  should be invoiced
                </p>
              </div>
            </div>
          </div>

          {/* Other reasons if any */}
          {otherReasons.map(([reason, count], index) => (
            <div
              key={reason}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden"
            >
              <div className="flex items-center justify-between px-3 py-2 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center">
                  <div className="flex-shrink-0 w-1 h-6 bg-gray-400 dark:bg-gray-500 rounded mr-2"></div>
                  <div className="flex items-center">
                    <div className="mr-2">
                      <div className="flex items-center justify-center w-5 h-5 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs font-semibold">
                        {index + 4}
                      </div>
                    </div>
                    <div>
                      <h4 className="text-xs font-medium text-gray-800 dark:text-gray-200 truncate">
                        Other Exclusion
                      </h4>
                    </div>
                  </div>
                </div>
                <div>
                  <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300">
                    {count}
                  </span>
                </div>
              </div>
              <div className="px-3 py-2">
                <div
                  className="text-xs text-gray-700 dark:text-gray-300 truncate"
                  title={reason}
                >
                  {reason}
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        // Compact view in a single row for all rule cards
        <div className="flex flex-wrap gap-2">
          {/* Rule 1: Uninvoiced Work Rule */}
          <div className="flex-1 min-w-[170px] bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden flex h-[60px]">
            <div className="w-1 bg-cyan-500 dark:bg-cyan-600"></div>
            <div className="px-2 py-2 flex-1 flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className="flex-shrink-0 flex items-center justify-center w-5 h-5 rounded-full bg-cyan-100 dark:bg-cyan-900/30 text-cyan-700 dark:text-cyan-300">
                  <span className="text-[10px] font-medium">1</span>
                </div>
                <div>
                  <h3 className="text-xs font-medium text-gray-900 dark:text-gray-100">
                    Uninvoiced Work
                  </h3>
                  <p className="text-[10px] text-gray-500 dark:text-gray-400">
                    Double-counting
                  </p>
                </div>
              </div>
              <div>
                {uninvoicedWorkCount > 0 ? (
                  <span className="inline-flex items-center px-1.5 py-0.5 rounded text-[10px] font-medium bg-amber-100 dark:bg-amber-900/30 text-amber-800 dark:text-amber-300">
                    {uninvoicedWorkCount}
                  </span>
                ) : (
                  <span className="inline-flex items-center px-1.5 py-0.5 rounded text-[10px] font-medium bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-300">
                    0
                  </span>
                )}
              </div>
            </div>
          </div>

          {/* Rule 2: Real Invoice Rule */}
          <div className="flex-1 min-w-[170px] bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden flex h-[60px]">
            <div className="w-1 bg-purple-500 dark:bg-purple-600"></div>
            <div className="px-2 py-2 flex-1 flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className="flex-shrink-0 flex items-center justify-center w-5 h-5 rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300">
                  <span className="text-[10px] font-medium">2</span>
                </div>
                <div>
                  <h3 className="text-xs font-medium text-gray-900 dark:text-gray-100">
                    Real Invoice
                  </h3>
                  <p className="text-[10px] text-gray-500 dark:text-gray-400">
                    Actual invoices
                  </p>
                </div>
              </div>
              <div>
                {realInvoiceCount > 0 ? (
                  <span className="inline-flex items-center px-1.5 py-0.5 rounded text-[10px] font-medium bg-amber-100 dark:bg-amber-900/30 text-amber-800 dark:text-amber-300">
                    {realInvoiceCount}
                  </span>
                ) : (
                  <span className="inline-flex items-center px-1.5 py-0.5 rounded text-[10px] font-medium bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-300">
                    0
                  </span>
                )}
              </div>
            </div>
          </div>

          {/* Rule 3: Payment Terms Rule */}
          <div className="flex-1 min-w-[170px] bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden flex h-[60px]">
            <div className="w-1 bg-blue-500 dark:bg-blue-600"></div>
            <div className="px-2 py-2 flex-1 flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className="flex-shrink-0 flex items-center justify-center w-5 h-5 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300">
                  <span className="text-[10px] font-medium">3</span>
                </div>
                <div>
                  <h3 className="text-xs font-medium text-gray-900 dark:text-gray-100">
                    Payment Terms
                  </h3>
                  <p className="text-[10px] text-gray-500 dark:text-gray-400">
                    Due date logic
                  </p>
                </div>
              </div>
              <div>
                {paymentTermsCount > 0 ? (
                  <span className="inline-flex items-center px-1.5 py-0.5 rounded text-[10px] font-medium bg-amber-100 dark:bg-amber-900/30 text-amber-800 dark:text-amber-300">
                    {paymentTermsCount}
                  </span>
                ) : (
                  <span className="inline-flex items-center px-1.5 py-0.5 rounded text-[10px] font-medium bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-300">
                    0
                  </span>
                )}
              </div>
            </div>
          </div>

          {/* Other reasons if any */}
          {otherReasons.map(([reason, count], index) => (
            <div
              key={reason}
              className="flex-1 min-w-[170px] bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden flex h-[60px]"
            >
              <div className="w-1 bg-gray-400 dark:bg-gray-500"></div>
              <div className="px-2 py-2 flex-1 flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="flex-shrink-0 flex items-center justify-center w-5 h-5 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300">
                    <span className="text-[10px] font-medium">{index + 4}</span>
                  </div>
                  <div>
                    <h3 className="text-xs font-medium text-gray-900 dark:text-gray-100">
                      Other Exclusion
                    </h3>
                    <p
                      className="text-[10px] text-gray-500 dark:text-gray-400 truncate max-w-[100px]"
                      title={reason}
                    >
                      {reason}
                    </p>
                  </div>
                </div>
                <div>
                  <span className="inline-flex items-center px-1.5 py-0.5 rounded text-[10px] font-medium bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300">
                    {count}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default FilteringRules;
