import React from "react";
import { Contact } from "../../../types/crm-types";
import { Card } from "../../shared/Card";
import DealContactsSection from "./DealContactsSection";
import { UserGroupIcon } from "@heroicons/react/24/outline";

interface DealContactsCardProps {
  dealId: string;
  contacts: Contact[];
}

/**
 * Card wrapper for the DealContactsSection component
 */
const DealContactsCard: React.FC<DealContactsCardProps> = ({
  dealId,
  contacts,
}) => {
  return (
    <Card className="overflow-hidden border border-gray-200 dark:border-gray-700">
      <div className="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700 flex items-center">
        <UserGroupIcon
          className="h-6 w-6 text-blue-500 mr-2"
          aria-hidden="true"
        />
        <div>
          <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white">
            Contacts
          </h3>
          <p className="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">
            People associated with this deal.
          </p>
        </div>
      </div>
      <div className="px-4 py-5 sm:p-6">
        <DealContactsSection dealId={dealId} contacts={contacts} />
      </div>
    </Card>
  );
};

export default DealContactsCard;
