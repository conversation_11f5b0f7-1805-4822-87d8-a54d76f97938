import React from "react";
import { formatCurrency } from "./utils";
import { format } from "date-fns";
import { Card } from "../shared/Card";

interface DraftEstimateCardProps {
  draft: any;
  onView: (uuid: string) => void;
  onDelete: (uuid: string) => void;
  onLinkDeal: () => void;
  onViewLinkedDeal: () => void;
  hasLinkedDeal: boolean;
  StaffInitialsCircles: React.FC<{ initials: string[] }>;
}

export const DraftEstimateCard: React.FC<DraftEstimateCardProps> = ({
  draft,
  onView,
  onDelete,
  onLinkDeal,
  onViewLinkedDeal,
  hasLinkedDeal,
  StaffInitialsCircles,
}) => {
  return (
    <Card className="p-4">
      {/* Header */}
      <div className="flex justify-between items-start mb-3">
        <div className="flex-1 min-w-0">
          <h3 className="font-medium text-gray-900 dark:text-gray-100 truncate">
            {draft.projectName || "Untitled Project"}
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
            {draft.clientName}
          </p>
        </div>
        <div className="flex-shrink-0 ml-3">
          {draft.isLoading ? (
            <span className="text-sm text-gray-500">Loading...</span>
          ) : draft.totalFees !== undefined ? (
            <span className="font-medium text-gray-900 dark:text-gray-100">
              {formatCurrency(draft.totalFees)}
            </span>
          ) : (
            <span className="text-sm text-gray-400">-</span>
          )}
        </div>
      </div>

      {/* Date Range */}
      <div className="text-sm text-gray-600 dark:text-gray-400 mb-3">
        <svg
          className="inline-block w-4 h-4 mr-1"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
          />
        </svg>
        {format(new Date(draft.startDate), "MMM d")} -{" "}
        {format(new Date(draft.endDate), "MMM d, yyyy")}
      </div>

      {/* Staff and Margin */}
      <div className="flex items-center justify-between mb-3">
        <div>
          {draft.isLoading ? (
            <span className="text-sm text-gray-500">Loading staff...</span>
          ) : draft.staffInitials && draft.staffInitials.length > 0 ? (
            <StaffInitialsCircles initials={draft.staffInitials} />
          ) : (
            <span className="text-sm text-gray-400">No staff</span>
          )}
        </div>
        <div>
          {!draft.isLoading &&
            draft.grossMarginAmount !== undefined &&
            draft.grossMarginPercentage !== undefined && (
              <span
                className={`text-sm font-medium ${
                  draft.grossMarginPercentage >= 50
                    ? "text-green-600 dark:text-green-400"
                    : draft.grossMarginPercentage >= 30
                    ? "text-amber-600 dark:text-amber-400"
                    : "text-red-600 dark:text-red-400"
                }`}
              >
                {draft.grossMarginPercentage.toFixed(1)}%
              </span>
            )}
        </div>
      </div>

      {/* Actions */}
      <div className="flex flex-wrap gap-2">
        <button
          onClick={() => onView(draft.uuid)}
          className="flex-1 inline-flex items-center justify-center px-3 py-1.5 rounded text-xs font-medium border border-blue-600 text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="w-3.5 h-3.5 mr-1"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"
            />
          </svg>
          View/Edit
        </button>

        {hasLinkedDeal ? (
          <button
            onClick={onViewLinkedDeal}
            className="flex-1 inline-flex items-center justify-center px-3 py-1.5 rounded text-xs font-medium border border-emerald-600 text-emerald-600 hover:bg-emerald-50 dark:hover:bg-emerald-900/20 transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="w-3.5 h-3.5 mr-1"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"
              />
            </svg>
            Linked Deal
          </button>
        ) : (
          <button
            onClick={onLinkDeal}
            className="flex-1 inline-flex items-center justify-center px-3 py-1.5 rounded text-xs font-medium border border-emerald-600 text-emerald-600 hover:bg-emerald-50 dark:hover:bg-emerald-900/20 transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="w-3.5 h-3.5 mr-1"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"
              />
            </svg>
            Link to Deal
          </button>
        )}

        <button
          onClick={() => {
            if (
              window.confirm(
                "Are you sure you want to delete this draft estimate? This action cannot be undone."
              )
            ) {
              onDelete(draft.uuid);
            }
          }}
          className="inline-flex items-center justify-center px-3 py-1.5 rounded text-xs font-medium border border-red-600 text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="w-3.5 h-3.5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
            />
          </svg>
        </button>
      </div>

      {/* Last Updated */}
      <div className="mt-3 text-xs text-gray-500 dark:text-gray-400">
        Updated {format(new Date(draft.updatedAt), "MMM d, yyyy")}
      </div>
    </Card>
  );
};