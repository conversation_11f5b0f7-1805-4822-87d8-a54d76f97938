import React, { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "react-query";
import {
  checkHubSpotStatus,
  importAllFromHubSpot,
  getHubSpotImportHistory,
} from "../../../api/hubspot";
import { HubSpotImport as HubSpotImportType } from "../../../types/crm-types";
import ImportProgressTracker from "./ImportProgressTracker";
import ImportSummary from "./ImportSummary";
import { CheckCircleIcon } from "@heroicons/react/24/solid";

/**
 * Main component for HubSpot integration
 */
const HubSpotIntegration: React.FC = () => {
  const [showProgress, setShowProgress] = useState(false);
  const [importResults, setImportResults] = useState<any>(null);
  const queryClient = useQueryClient();

  // Fetch HubSpot status
  const { data: hubspotStatus, isLoading: isStatusLoading } = useQuery(
    "hubspotStatus",
    checkHubSpotStatus
  );

  // Fetch import history
  const { data: importHistory = [], isLoading: isHistoryLoading } = useQuery(
    "hubspotImportHistory",
    getHubSpotImportHistory
  );

  // Mutation for importing all data
  const importAllMutation = useMutation(() => importAllFromHubSpot(), {
    onMutate: () => {
      setShowProgress(true);
      setImportResults(null);
    },
    onSuccess: (data) => {
      setImportResults(data);
      setShowProgress(false);
      queryClient.removeQueries("deals");
      queryClient.removeQueries("contacts");
      queryClient.removeQueries("companies");
      queryClient.invalidateQueries("hubspotImportHistory");
    },
    onError: () => {
      setShowProgress(false);
    },
  });

  // Handle importing all data
  const handleImportAll = () => {
    importAllMutation.mutate();
  };

  // Format date for display
  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleString("en-AU", {
      day: "numeric",
      month: "short",
      year: "numeric",
      hour: "numeric",
      minute: "2-digit",
    });
  };

  // Get status badge color
  const getStatusBadgeColor = (status: HubSpotImportType["status"]): string => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      case "failed":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
      case "pending":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200";
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-4 md:p-6">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-start justify-between">
          <div>
            <div className="flex items-center gap-3">
              <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
                HubSpot Integration
              </h1>
              {/* Connection Status Badge */}
              {!isStatusLoading && hubspotStatus?.isConfigured && (
                <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300">
                  <CheckCircleIcon className="w-3.5 h-3.5 mr-1.5" />
                  Connected
                </div>
              )}
            </div>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Connect your HubSpot account and import deals, contacts, and
              companies
            </p>
          </div>
        </div>

        {isStatusLoading ? (
          <div className="flex justify-center items-center h-24">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Configuration Instructions for non-connected state */}
            {!hubspotStatus?.isConfigured && (
              <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
                <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  Getting Started
                </h2>
                <div className="space-y-4">
                  <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-md p-4">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <svg
                          className="h-5 w-5 text-amber-400"
                          xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fillRule="evenodd"
                            d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-amber-800 dark:text-amber-200">
                          HubSpot integration not configured
                        </h3>
                        <p className="mt-2 text-sm text-amber-700 dark:text-amber-300">
                          Connect your HubSpot account to import deals,
                          contacts, and companies.
                        </p>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      How to configure HubSpot integration
                    </h3>
                    <ol className="list-decimal list-inside text-sm text-gray-600 dark:text-gray-400 space-y-1">
                      <li>Go to your HubSpot account settings</li>
                      <li>Navigate to Integrations &gt; Private Apps</li>
                      <li>Click "Create private app"</li>
                      <li>Name your app (e.g., "Onbord Integration")</li>
                      <li>
                        Select the following scopes:
                        <ul className="list-disc list-inside ml-4 mt-1">
                          <li>crm.objects.contacts.read</li>
                          <li>crm.objects.companies.read</li>
                          <li>crm.objects.deals.read</li>
                        </ul>
                      </li>
                      <li>Create the app and copy the access token</li>
                      <li>
                        Set the HUBSPOT_ACCESS_TOKEN environment variable with
                        the token
                      </li>
                      <li>Restart the application</li>
                    </ol>
                  </div>
                </div>
              </div>
            )}

            {/* Import Section */}
            {hubspotStatus?.isConfigured && (
              <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
                <div className="mb-4">
                  <h2 className="text-lg font-medium text-gray-900 dark:text-white">
                    Data Import
                  </h2>
                </div>


                <div className="space-y-4">
                  {/* Show ImportProgressTracker when importing */}
                  {showProgress && (
                    <ImportProgressTracker
                      isImporting={showProgress}
                      onComplete={(results: any) => {
                        setImportResults(results);
                        setShowProgress(false);
                      }}
                    />
                  )}

                  {/* Show ImportSummary when results are available */}
                  {importResults && !showProgress && (
                    <ImportSummary
                      results={importResults.results}
                      totalCount={importResults.totalCount}
                      onClose={() => setImportResults(null)}
                    />
                  )}

                  {/* Import button */}
                  {!showProgress && !importResults && (
                    <div className="space-y-4">
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        Import all data from HubSpot in the correct order:
                        companies first, then deals, then contacts.
                      </p>
                      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md p-3">
                        <p className="text-xs text-blue-700 dark:text-blue-300">
                          <strong>Note:</strong> Deals without company
                          associations will be skipped. Make sure all deals are
                          linked to companies in HubSpot before importing.
                        </p>
                      </div>
                      <button
                        type="button"
                        onClick={handleImportAll}
                        className="w-full px-6 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-blue-600 hover:bg-blue-700"
                        disabled={importAllMutation.isLoading}
                      >
                        Import All Data
                      </button>
                    </div>
                  )}

                  {/* Button to start new import */}
                  {importResults && !showProgress && (
                    <div className="text-center">
                      <button
                        type="button"
                        onClick={() => setImportResults(null)}
                        className="text-sm text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
                      >
                        Start New Import
                      </button>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Import History */}
            {hubspotStatus?.isConfigured && importHistory.length > 0 && (
              <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
                <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  Import History
                </h2>

                {isHistoryLoading ? (
                  <div className="flex justify-center items-center h-24">
                    <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                      <thead className="bg-gray-50 dark:bg-gray-700">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Date
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Status
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Deals
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Contacts
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Companies
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        {importHistory.slice(0, 5).map((importItem) => (
                          <tr key={importItem.id}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                              {formatDate(importItem.importDate)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span
                                className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeColor(
                                  importItem.status
                                )}`}
                              >
                                {importItem.status}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                              {importItem.dealsCount || "-"}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                              {importItem.contactsCount || "-"}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                              {importItem.companiesCount || "-"}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default HubSpotIntegration;
