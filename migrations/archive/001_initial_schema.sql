-- Initial Schema Migration - Corrected with TEXT UUIDs
-- This creates all tables matching the unified data model documentation

-- Enable foreign keys
PRAGMA foreign_keys = ON;

-- Migration tracking table
CREATE TABLE IF NOT EXISTS migrations (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    executed_at TEXT NOT NULL DEFAULT (datetime('now')),
    batch INTEGER NOT NULL DEFAULT 1
);

-- Company table with TEXT UUIDs and complete field set
CREATE TABLE IF NOT EXISTS company (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    industry TEXT,
    size TEXT,
    website TEXT,
    address TEXT,
    description TEXT,
    hubspot_id TEXT UNIQUE,
    harvest_id TEXT UNIQUE,
    source TEXT,
    radar_state TEXT,
    priority TEXT CHECK(priority IN ('low', 'medium', 'high', 'qualified out')) DEFAULT 'medium',
    current_spend REAL DEFAULT 0,
    potential_spend REAL DEFAULT 0,
    last_interaction_date TEXT,
    notes TEXT,
    created_at TEXT DEFAULT (datetime('now')),
    updated_at TEXT DEFAULT (datetime('now')),
    created_by TEXT,
    updated_by TEXT,
    deleted_at TEXT,
    contacts INTEGER
);

-- Contact table with TEXT UUIDs and complete field set
CREATE TABLE IF NOT EXISTS contact (
    id TEXT PRIMARY KEY,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    email TEXT UNIQUE,
    phone TEXT,
    job_title TEXT,
    notes TEXT,
    hubspot_id TEXT UNIQUE,
    harvest_user_id TEXT,
    source TEXT,
    created_at TEXT DEFAULT (datetime('now')),
    updated_at TEXT DEFAULT (datetime('now')),
    created_by TEXT,
    updated_by TEXT,
    deleted_at TEXT
);

-- Deal table with TEXT UUIDs and complete field set
CREATE TABLE IF NOT EXISTS deal (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    stage TEXT NOT NULL,
    value REAL,
    currency TEXT DEFAULT 'AUD',
    probability REAL CHECK(probability >= 0 AND probability <= 100),
    expected_close_date TEXT,
    start_date TEXT,
    end_date TEXT,
    invoice_frequency TEXT,
    payment_terms INTEGER,
    company_id TEXT NOT NULL,
    hubspot_id TEXT UNIQUE,
    harvest_project_id TEXT,
    description TEXT,
    source TEXT,
    priority TEXT,
    owner TEXT,
    custom_fields TEXT,
    include_in_projections INTEGER DEFAULT 1,
    status TEXT,
    created_at TEXT DEFAULT (datetime('now')),
    updated_at TEXT DEFAULT (datetime('now')),
    created_by TEXT NOT NULL,
    updated_by TEXT NOT NULL,
    deleted_at TEXT,
    FOREIGN KEY (company_id) REFERENCES company(id) ON DELETE CASCADE
);

-- Contact-Company relationship table
CREATE TABLE IF NOT EXISTS contact_company (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    contact_id TEXT NOT NULL,
    company_id TEXT NOT NULL,
    role TEXT,
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TEXT DEFAULT (datetime('now')),
    created_by TEXT,
    updated_at TEXT DEFAULT (datetime('now')),
    updated_by TEXT,
    FOREIGN KEY (contact_id) REFERENCES contact(id) ON DELETE CASCADE,
    FOREIGN KEY (company_id) REFERENCES company(id) ON DELETE CASCADE,
    UNIQUE(contact_id, company_id)
);

-- Contact roles in deals
CREATE TABLE IF NOT EXISTS contact_role (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    contact_id TEXT NOT NULL,
    deal_id TEXT NOT NULL,
    role TEXT,
    created_at TEXT DEFAULT (datetime('now')),
    created_by TEXT,
    FOREIGN KEY (contact_id) REFERENCES contact(id) ON DELETE CASCADE,
    FOREIGN KEY (deal_id) REFERENCES deal(id) ON DELETE CASCADE,
    UNIQUE(contact_id, deal_id)
);

-- Deal-Contact relationship table (as documented)
CREATE TABLE IF NOT EXISTS deal_contact (
    deal_id TEXT NOT NULL,
    contact_id TEXT NOT NULL,
    role TEXT DEFAULT 'decision_maker',
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TEXT DEFAULT (datetime('now')),
    updated_at TEXT DEFAULT (datetime('now')),
    PRIMARY KEY (deal_id, contact_id),
    FOREIGN KEY (deal_id) REFERENCES deal(id) ON DELETE CASCADE,
    FOREIGN KEY (contact_id) REFERENCES contact(id) ON DELETE CASCADE
);

-- Estimates table with corrected structure per documentation
CREATE TABLE IF NOT EXISTS estimate (
    id TEXT PRIMARY KEY,
    company_id TEXT NOT NULL,
    client_name TEXT NOT NULL,
    project_name TEXT,
    project_code TEXT,
    estimate_number TEXT,
    date_sent TEXT,
    valid_until TEXT,
    start_date TEXT,
    end_date TEXT,
    discount_type TEXT DEFAULT 'none',
    discount_value REAL DEFAULT 0,
    invoice_frequency TEXT,
    total_consultancy REAL DEFAULT 0,
    total_expenses REAL DEFAULT 0,
    total REAL DEFAULT 0,
    tax REAL DEFAULT 0,
    grand_total REAL DEFAULT 0,
    payment_schedule TEXT,
    payment_terms TEXT,
    payment_percentage REAL,
    staff_allocations TEXT,
    status TEXT DEFAULT 'draft',
    version INTEGER DEFAULT 1,
    notes TEXT,
    harvest_estimate_id TEXT UNIQUE,
    created_at TEXT DEFAULT (datetime('now')),
    updated_at TEXT DEFAULT (datetime('now')),
    created_by TEXT,
    updated_by TEXT,
    deleted_at TEXT,
    FOREIGN KEY (company_id) REFERENCES company(id) ON DELETE CASCADE
);

-- Estimate allocations with TEXT UUIDs
CREATE TABLE IF NOT EXISTS estimate_allocation (
    id TEXT PRIMARY KEY,
    estimate_id TEXT NOT NULL,
    harvest_user_id INTEGER NOT NULL,
    first_name TEXT NOT NULL,
    last_name TEXT,
    project_role TEXT,
    level TEXT,
    target_rate_daily REAL NOT NULL,
    cost_rate_daily REAL NOT NULL,
    proposed_rate_daily REAL NOT NULL,
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL,
    FOREIGN KEY (estimate_id) REFERENCES estimate(id) ON DELETE CASCADE
);

-- Time allocations with TEXT UUIDs
CREATE TABLE IF NOT EXISTS estimate_time_allocation (
    id TEXT PRIMARY KEY,
    allocation_id TEXT NOT NULL,
    week_identifier TEXT NOT NULL,
    days REAL NOT NULL,
    FOREIGN KEY (allocation_id) REFERENCES estimate_allocation(id) ON DELETE CASCADE,
    UNIQUE(allocation_id, week_identifier)
);

-- Deal-Estimate relationships with composite primary key
CREATE TABLE IF NOT EXISTS deal_estimate (
    deal_id TEXT NOT NULL,
    estimate_id TEXT NOT NULL,
    estimate_type TEXT NOT NULL CHECK(estimate_type IN ('internal', 'harvest')),
    harvest_estimate_id TEXT,
    linked_at TEXT DEFAULT (datetime('now')),
    linked_by TEXT DEFAULT 'system',
    created_at TEXT DEFAULT (datetime('now')),
    PRIMARY KEY (deal_id, estimate_id, estimate_type),
    FOREIGN KEY (deal_id) REFERENCES deal(id) ON DELETE CASCADE,
    FOREIGN KEY (estimate_id) REFERENCES estimate(id) ON DELETE CASCADE
);

-- Expenses table with TEXT UUIDs
CREATE TABLE IF NOT EXISTS expense (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    amount REAL NOT NULL,
    frequency TEXT CHECK(frequency IN ('weekly', 'fortnightly', 'monthly', 'quarterly', 'one-off')) NOT NULL,
    type TEXT CHECK(type IN ('Monthly Payroll', 'Superannuation', 'Insurances', 'Taxes', 'Subcontractor Fees', 'Rent', 'Reimbursements', 'Professional Fees', 'General Expenses', 'Director Distributions', 'Hardware', 'Subscriptions', 'Other Fees', 'Other')) NOT NULL,
    date TEXT NOT NULL,
    repeat_count INTEGER,
    source TEXT DEFAULT 'manual',
    description TEXT,
    metadata TEXT,
    editable INTEGER DEFAULT 1,
    end_date TEXT,
    created_at TEXT DEFAULT (datetime('now')),
    updated_at TEXT DEFAULT (datetime('now')),
    deleted_at TEXT
);

-- Notes table with TEXT UUIDs and corrected entity_id type
CREATE TABLE IF NOT EXISTS note (
    id TEXT PRIMARY KEY,
    content TEXT NOT NULL,
    entity_type TEXT NOT NULL,
    entity_id TEXT NOT NULL,
    author TEXT,
    created_at TEXT DEFAULT (datetime('now')),
    updated_at TEXT DEFAULT (datetime('now')),
    created_by TEXT,
    updated_by TEXT,
    deleted_at TEXT
);

-- Activity feed (already correct in previous migration)
CREATE TABLE IF NOT EXISTS activity_feed (
    id TEXT PRIMARY KEY,
    type TEXT NOT NULL,
    subject TEXT NOT NULL,
    description TEXT,
    status TEXT,
    entity_type TEXT,
    entity_id TEXT,
    due_date TEXT,
    completed_date TEXT,
    company_id TEXT,
    contact_id TEXT,
    deal_id TEXT,
    metadata TEXT,
    is_read INTEGER DEFAULT 0,
    importance TEXT DEFAULT 'normal',
    created_by TEXT NOT NULL,
    source TEXT,
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL,
    FOREIGN KEY (company_id) REFERENCES company(id) ON DELETE CASCADE,
    FOREIGN KEY (contact_id) REFERENCES contact(id) ON DELETE CASCADE,
    FOREIGN KEY (deal_id) REFERENCES deal(id) ON DELETE CASCADE
);

-- Harvest invoice cache with corrected structure per documentation
CREATE TABLE IF NOT EXISTS harvest_invoice_cache (
    harvest_client_id INTEGER PRIMARY KEY,
    total_invoiced REAL NOT NULL DEFAULT 0,
    invoice_count INTEGER NOT NULL DEFAULT 0,
    last_updated TEXT NOT NULL,
    created_at TEXT NOT NULL DEFAULT (datetime('now'))
);

-- Settings table (unchanged)
CREATE TABLE IF NOT EXISTS settings (
    key TEXT PRIMARY KEY,
    value TEXT,
    created_at TEXT DEFAULT (datetime('now')),
    updated_at TEXT DEFAULT (datetime('now'))
);

-- HubSpot settings table as documented
CREATE TABLE IF NOT EXISTS hubspot_settings (
    id TEXT PRIMARY KEY,
    key TEXT NOT NULL UNIQUE,
    value TEXT,
    created_at TEXT DEFAULT (datetime('now')),
    updated_at TEXT DEFAULT (datetime('now'))
);

-- Field ownership table as documented
CREATE TABLE IF NOT EXISTS field_ownership (
    id TEXT PRIMARY KEY,
    entity_type TEXT NOT NULL,
    entity_id TEXT NOT NULL,
    field_name TEXT NOT NULL,
    owner TEXT NOT NULL,
    set_at TEXT NOT NULL,
    set_by TEXT,
    UNIQUE(entity_type, entity_id, field_name)
);

-- Change log table as documented
CREATE TABLE IF NOT EXISTS change_log (
    id TEXT PRIMARY KEY,
    entity_type TEXT NOT NULL,
    entity_id TEXT NOT NULL,
    field_name TEXT NOT NULL,
    old_value TEXT,
    new_value TEXT,
    change_source TEXT NOT NULL,
    changed_at TEXT NOT NULL,
    changed_by TEXT
);

-- Company relationship table as documented
CREATE TABLE IF NOT EXISTS company_relationship (
    id TEXT PRIMARY KEY,
    parent_company_id TEXT NOT NULL,
    child_company_id TEXT NOT NULL,
    relationship_type TEXT NOT NULL,
    created_at TEXT NOT NULL,
    created_by TEXT NOT NULL,
    FOREIGN KEY (parent_company_id) REFERENCES company(id) ON DELETE CASCADE,
    FOREIGN KEY (child_company_id) REFERENCES company(id) ON DELETE CASCADE,
    UNIQUE(parent_company_id, child_company_id)
);

-- Cashflow snapshot table
CREATE TABLE IF NOT EXISTS cashflow_snapshot (
    id TEXT PRIMARY KEY,
    date TEXT NOT NULL,
    tenant_id TEXT NOT NULL,
    days_ahead INTEGER NOT NULL DEFAULT 90,
    snapshot_data TEXT NOT NULL,
    created_at TEXT NOT NULL,
    created_by TEXT,
    UNIQUE(date, tenant_id, days_ahead)
);

-- Create comprehensive indexes for performance
CREATE INDEX IF NOT EXISTS idx_company_hubspot_id ON company(hubspot_id);
CREATE INDEX IF NOT EXISTS idx_company_harvest_id ON company(harvest_id);
CREATE INDEX IF NOT EXISTS idx_company_source ON company(source);
CREATE INDEX IF NOT EXISTS idx_company_radar_state ON company(radar_state);
CREATE INDEX IF NOT EXISTS idx_company_priority ON company(priority);
CREATE INDEX IF NOT EXISTS idx_company_created_at ON company(created_at);

CREATE INDEX IF NOT EXISTS idx_contact_hubspot_id ON contact(hubspot_id);
CREATE INDEX IF NOT EXISTS idx_contact_harvest_user_id ON contact(harvest_user_id);
CREATE INDEX IF NOT EXISTS idx_contact_email ON contact(email);
CREATE INDEX IF NOT EXISTS idx_contact_source ON contact(source);

CREATE INDEX IF NOT EXISTS idx_deal_hubspot_id ON deal(hubspot_id);
CREATE INDEX IF NOT EXISTS idx_deal_company_id ON deal(company_id);
CREATE INDEX IF NOT EXISTS idx_deal_stage ON deal(stage);
CREATE INDEX IF NOT EXISTS idx_deal_status ON deal(status);
CREATE INDEX IF NOT EXISTS idx_deal_expected_close_date ON deal(expected_close_date);
CREATE INDEX IF NOT EXISTS idx_deal_harvest_project_id ON deal(harvest_project_id);
CREATE INDEX IF NOT EXISTS idx_deal_include_in_projections ON deal(include_in_projections);
CREATE INDEX IF NOT EXISTS idx_deal_priority ON deal(priority);
CREATE INDEX IF NOT EXISTS idx_deal_owner ON deal(owner);

CREATE INDEX IF NOT EXISTS idx_contact_company_contact_id ON contact_company(contact_id);
CREATE INDEX IF NOT EXISTS idx_contact_company_company_id ON contact_company(company_id);
CREATE INDEX IF NOT EXISTS idx_contact_company_is_primary ON contact_company(is_primary);

CREATE INDEX IF NOT EXISTS idx_contact_role_contact_id ON contact_role(contact_id);
CREATE INDEX IF NOT EXISTS idx_contact_role_deal_id ON contact_role(deal_id);

CREATE INDEX IF NOT EXISTS idx_deal_contact_deal_id ON deal_contact(deal_id);
CREATE INDEX IF NOT EXISTS idx_deal_contact_contact_id ON deal_contact(contact_id);
CREATE INDEX IF NOT EXISTS idx_deal_contact_is_primary ON deal_contact(is_primary);

CREATE INDEX IF NOT EXISTS idx_estimate_company_id ON estimate(company_id);
CREATE INDEX IF NOT EXISTS idx_estimate_harvest_id ON estimate(harvest_estimate_id);
CREATE INDEX IF NOT EXISTS idx_estimate_status ON estimate(status);
CREATE INDEX IF NOT EXISTS idx_estimate_created_at ON estimate(created_at);

CREATE INDEX IF NOT EXISTS idx_estimate_allocation_estimate_id ON estimate_allocation(estimate_id);
CREATE INDEX IF NOT EXISTS idx_estimate_allocation_harvest_user_id ON estimate_allocation(harvest_user_id);

CREATE INDEX IF NOT EXISTS idx_estimate_time_allocation_allocation_id ON estimate_time_allocation(allocation_id);
CREATE INDEX IF NOT EXISTS idx_estimate_time_allocation_week ON estimate_time_allocation(week_identifier);

CREATE INDEX IF NOT EXISTS idx_deal_estimate_deal_id ON deal_estimate(deal_id);
CREATE INDEX IF NOT EXISTS idx_deal_estimate_estimate_id ON deal_estimate(estimate_id);
CREATE INDEX IF NOT EXISTS idx_deal_estimate_type ON deal_estimate(estimate_type);

CREATE INDEX IF NOT EXISTS idx_expense_type ON expense(type);
CREATE INDEX IF NOT EXISTS idx_expense_frequency ON expense(frequency);
CREATE INDEX IF NOT EXISTS idx_expense_date ON expense(date);
CREATE INDEX IF NOT EXISTS idx_expense_end_date ON expense(end_date);

CREATE INDEX IF NOT EXISTS idx_note_entity ON note(entity_type, entity_id);
CREATE INDEX IF NOT EXISTS idx_note_author ON note(author);
CREATE INDEX IF NOT EXISTS idx_note_created_at ON note(created_at);

CREATE INDEX IF NOT EXISTS idx_activity_feed_entity ON activity_feed(entity_type, entity_id);
CREATE INDEX IF NOT EXISTS idx_activity_feed_created_at ON activity_feed(created_at);
CREATE INDEX IF NOT EXISTS idx_activity_feed_type ON activity_feed(type);
CREATE INDEX IF NOT EXISTS idx_activity_feed_source ON activity_feed(source);
CREATE INDEX IF NOT EXISTS idx_activity_feed_created_by ON activity_feed(created_by);
CREATE INDEX IF NOT EXISTS idx_activity_feed_unread ON activity_feed(is_read);
CREATE INDEX IF NOT EXISTS idx_activity_feed_importance ON activity_feed(importance);
CREATE INDEX IF NOT EXISTS idx_activity_feed_status ON activity_feed(status);

CREATE INDEX IF NOT EXISTS idx_harvest_cache_last_updated ON harvest_invoice_cache(last_updated);

CREATE INDEX IF NOT EXISTS idx_hubspot_settings_key ON hubspot_settings(key);

CREATE INDEX IF NOT EXISTS idx_field_ownership_entity ON field_ownership(entity_type, entity_id);
CREATE INDEX IF NOT EXISTS idx_field_ownership_owner ON field_ownership(owner);
CREATE INDEX IF NOT EXISTS idx_field_ownership_field_name ON field_ownership(field_name);

CREATE INDEX IF NOT EXISTS idx_change_log_entity ON change_log(entity_type, entity_id);
CREATE INDEX IF NOT EXISTS idx_change_log_field ON change_log(field_name);
CREATE INDEX IF NOT EXISTS idx_change_log_date ON change_log(changed_at);
CREATE INDEX IF NOT EXISTS idx_change_log_source ON change_log(change_source);
CREATE INDEX IF NOT EXISTS idx_change_log_changed_by ON change_log(changed_by);

CREATE INDEX IF NOT EXISTS idx_company_relationship_parent ON company_relationship(parent_company_id);
CREATE INDEX IF NOT EXISTS idx_company_relationship_child ON company_relationship(child_company_id);
CREATE INDEX IF NOT EXISTS idx_company_relationship_type ON company_relationship(relationship_type);

CREATE INDEX IF NOT EXISTS idx_cashflow_snapshot_date ON cashflow_snapshot(date);
CREATE INDEX IF NOT EXISTS idx_cashflow_snapshot_tenant ON cashflow_snapshot(tenant_id);
CREATE INDEX IF NOT EXISTS idx_cashflow_snapshot_days_ahead ON cashflow_snapshot(days_ahead);

-- Record this migration
INSERT OR IGNORE INTO migrations (id, name) VALUES ('001', 'corrected_initial_schema_with_text_uuids');