-- Migration to change deal-estimate relationship from many-to-many to one-to-one
-- Each estimate should belong to exactly one deal

-- Enable foreign keys
PRAGMA foreign_keys = ON;

-- Step 1: Create a new deal_estimate table with the correct structure
CREATE TABLE IF NOT EXISTS deal_estimate_new (
    id TEXT PRIMARY KEY,
    deal_id TEXT NOT NULL UNIQUE, -- UNIQUE constraint ensures one estimate per deal
    estimate_id TEXT NOT NULL UNIQUE, -- UNIQUE constraint ensures estimate belongs to only one deal
    estimate_type TEXT NOT NULL CHECK(estimate_type IN ('internal', 'harvest')),
    harvest_estimate_id TEXT,
    linked_at TEXT DEFAULT (datetime('now')),
    linked_by TEXT DEFAULT 'system',
    created_at TEXT DEFAULT (datetime('now')),
    updated_at TEXT DEFAULT (datetime('now')),
    FOREIGN KEY (deal_id) REFERENCES deal(id) ON DELETE CASCADE,
    FOREIGN KEY (estimate_id) REFERENCES estimate(id) ON DELETE CASCADE
);

-- Step 2: Copy existing data (keeping only the first estimate per deal if multiple exist)
INSERT INTO deal_estimate_new (id, deal_id, estimate_id, estimate_type, harvest_estimate_id, linked_at, linked_by, created_at)
SELECT 
    lower(hex(randomblob(16))), -- Generate new UUID for id
    deal_id,
    estimate_id,
    estimate_type,
    harvest_estimate_id,
    linked_at,
    linked_by,
    created_at
FROM deal_estimate
WHERE rowid IN (
    SELECT MIN(rowid)
    FROM deal_estimate
    GROUP BY deal_id
);

-- Step 3: Drop the old table
DROP TABLE deal_estimate;

-- Step 4: Rename the new table
ALTER TABLE deal_estimate_new RENAME TO deal_estimate;

-- Step 5: Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_deal_estimate_deal_id ON deal_estimate(deal_id);
CREATE INDEX IF NOT EXISTS idx_deal_estimate_estimate_id ON deal_estimate(estimate_id);
CREATE INDEX IF NOT EXISTS idx_deal_estimate_type ON deal_estimate(estimate_type);

-- Step 6: Add estimate_id column to deal table for direct reference (optional but helpful)
-- This makes it easier to query without joining
ALTER TABLE deal ADD COLUMN estimate_id TEXT REFERENCES estimate(id) ON DELETE SET NULL;
ALTER TABLE deal ADD COLUMN estimate_type TEXT CHECK(estimate_type IN ('internal', 'harvest', NULL));

-- Update deal table with existing estimate links
UPDATE deal
SET estimate_id = (
    SELECT estimate_id 
    FROM deal_estimate 
    WHERE deal_estimate.deal_id = deal.id
),
estimate_type = (
    SELECT estimate_type 
    FROM deal_estimate 
    WHERE deal_estimate.deal_id = deal.id
)
WHERE EXISTS (
    SELECT 1 
    FROM deal_estimate 
    WHERE deal_estimate.deal_id = deal.id
);