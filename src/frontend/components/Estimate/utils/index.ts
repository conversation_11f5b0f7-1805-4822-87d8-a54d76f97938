/**
 * Utility functions for the Price Estimation feature.
 */

import { StaffAllocation } from '../../../types/estimate-types';

// Constants
const HOURS_PER_DAY = 7.5; // Standard billable hours per day

/**
 * Converts an hourly rate to a daily rate based on standard hours.
 */
export const convertHourlyToDailyRate = (hourlyRate: number | null | undefined): number => {
  if (hourlyRate === null || hourlyRate === undefined) {
    return 0;
  }
  return hourlyRate * HOURS_PER_DAY;
};

/**
 * Calculates the daily gross margin percentage.
 */
export const calculateDailyGrossMarginPercentage = (proposedRateDaily: number, costRateDaily: number): number => {
  if (proposedRateDaily <= 0) {
    return 0;
  }
  const margin = proposedRateDaily - costRateDaily;
  return (margin / proposedRateDaily) * 100;
};

/**
 * Calculates the total allocated days for a staff member from their weekly allocation.
 */
export const calculateTotalDays = (weeklyAllocation: { [weekIdentifier: string]: number }): number => {
  return Object.values(weeklyAllocation).reduce((sum, days) => sum + (days || 0), 0);
};

/**
 * Calculates the total cost for a staff member based on their daily cost rate and total days.
 */
export const calculateTotalCost = (costRateDaily: number, totalDays: number): number => {
  return costRateDaily * totalDays;
};

/**
 * Calculates the total fees (revenue) for a staff member based on their proposed daily rate and total days.
 */
export const calculateTotalFees = (proposedRateDaily: number, totalDays: number): number => {
  return proposedRateDaily * totalDays;
};

/**
 * Calculates the project totals from staff allocations including GST.
 */
export const calculateProjectTotals = (
  staffAllocations: { 
    totalCost: number; 
    totalFees: number; 
    totalDays: number;
    onbordTargetRateDaily?: number;
  }[]
): {
  totalRevenue: number;
  totalCosts: number;
  marginAmount: number;
  marginPercentage: number;
  subTotal: number;
  gstAmount: number;
  grandTotal: number;
  totalDays: number;
  targetTotalRevenue: number;
  targetRateDifference: number;
  targetRatePercentageDifference: number;
  averageDailyRate: number;
} => {
  const totalRevenue = staffAllocations.reduce((sum, alloc) => sum + alloc.totalFees, 0);
  const totalCosts = staffAllocations.reduce((sum, alloc) => sum + alloc.totalCost, 0);
  const marginAmount = totalRevenue - totalCosts;
  const marginPercentage = totalRevenue > 0 ? (marginAmount / totalRevenue) * 100 : 0;
  
  // Calculate total days
  const totalDays = staffAllocations.reduce((sum, alloc) => sum + alloc.totalDays, 0);
  
  // Calculate average daily rate
  const averageDailyRate = totalDays > 0 ? totalRevenue / totalDays : 0;
  
  // Calculate target revenue based on target rates
  const targetTotalRevenue = staffAllocations.reduce((sum, alloc) => {
    const targetRate = alloc.onbordTargetRateDaily || 0;
    return sum + (targetRate * alloc.totalDays);
  }, 0);
  
  // Calculate rate difference from target
  const targetRateDifference = totalRevenue - targetTotalRevenue;
  const targetRatePercentageDifference = targetTotalRevenue > 0 
    ? (targetRateDifference / targetTotalRevenue) * 100 
    : 0;
  
  // Australian GST calculation (10%)
  const subTotal = totalRevenue;
  const gstAmount = subTotal * 0.1; // 10% GST
  const grandTotal = subTotal + gstAmount;

  return {
    totalRevenue,
    totalCosts,
    marginAmount,
    marginPercentage,
    subTotal,
    gstAmount,
    grandTotal,
    totalDays,
    targetTotalRevenue,
    targetRateDifference,
    targetRatePercentageDifference,
    averageDailyRate
  };
};

// Import centralized formatting utilities
import { formatCurrencyLegacy as formatCurrency } from '../../../utils/format';

/**
 * Formats a number as currency (e.g., $1,234.56).
 * @deprecated Use centralized formatCurrency from utils/format.ts instead
 */
export { formatCurrency };

/**
 * Formats a date in a consistent way for the estimate feature.
 */
export const formatEstimateDate = (date: Date | string): string => {
  // Convert string dates to Date objects
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  // Format the date as DD/MM/YYYY
  return dateObj.toLocaleDateString('en-AU', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
  });
};

/**
 * Get CSS class and text for status badges.
 */
export const getStatusStyles = (status: string): { className: string; label: string } => {
  switch (status.toLowerCase()) {
    case 'draft':
      return {
        className: 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',
        label: 'Draft',
      };
    case 'sent':
      return {
        className: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300',
        label: 'Sent',
      };
    case 'accepted':
      return {
        className: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300',
        label: 'Accepted',
      };
    case 'declined':
      return {
        className: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300',
        label: 'Declined',
      };
    default:
      return {
        className: 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',
        label: status.charAt(0).toUpperCase() + status.slice(1),
      };
  }
};

// Note: grid-helpers.ts file not found - removing export
