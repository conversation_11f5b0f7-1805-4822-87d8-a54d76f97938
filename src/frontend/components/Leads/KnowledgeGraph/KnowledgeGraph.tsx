import React, { useRef, useCallback, useMemo, useState, useEffect } from 'react';
import { useQuery } from 'react-query';
import ForceGraph2D from 'react-force-graph-2d';
import { Card } from '../../shared/Card';
import { useNavigate } from 'react-router-dom';
import { fetchFromApi } from '../../../api/utils';
import { useFeatureFlag } from '../../../api/feature-flags';
import { FeatureFlag } from '../../../../types/feature-flags';
import {
  BuildingOffice2Icon,
  UserIcon,
  CurrencyDollarIcon,
  DocumentTextIcon,
  ClipboardDocumentCheckIcon,
  FunnelIcon,
  XMarkIcon,
  ArrowDownTrayIcon,
  MagnifyingGlassIcon,
  AdjustmentsHorizontalIcon,
  ClockIcon,
  BookmarkIcon,
  SparklesIcon,
  Square3Stack3DIcon,
  ArrowsPointingInIcon,
  ArrowsPointingOutIcon,
  EyeIcon,
  EyeSlashIcon,
  ShareIcon
} from '@heroicons/react/24/outline';
import { MapPinIcon, MapPinIcon as MapPinIconSolid } from '@heroicons/react/24/solid';

interface KnowledgeGraphNode {
  id: string;
  label: string;
  type: 'company' | 'contact' | 'deal' | 'project' | 'estimate';
  group?: string;
  metadata?: Record<string, any>;
  x?: number;
  y?: number;
  fx?: number;
  fy?: number;
}

interface KnowledgeGraphLink {
  source: string;
  target: string;
  type: string;
  strength: number;
  label?: string;
  metadata?: Record<string, any>;
}

interface KnowledgeGraphData {
  nodes: KnowledgeGraphNode[];
  links: KnowledgeGraphLink[];
  stats: {
    totalNodes: number;
    totalLinks: number;
    nodeTypes: Record<string, number>;
    linkTypes: Record<string, number>;
  };
}

interface FilterState {
  entityTypes: string[];
  searchTerm: string;
  minNodeDegree: number;
  linkTypes: string[];
}

interface FilterPreset {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  filters: Partial<FilterState>;
}

interface ContextMenuState {
  visible: boolean;
  x: number;
  y: number;
  node: KnowledgeGraphNode | null;
}

interface FilterHistoryItem {
  id: string;
  timestamp: number;
  filters: FilterState;
  resultCount: number;
}

// Filter presets
const FILTER_PRESETS: FilterPreset[] = [
  {
    id: 'high-connectivity',
    name: 'High Connectivity',
    description: 'Show only highly connected entities',
    icon: <Square3Stack3DIcon className="w-4 h-4" />,
    filters: { minNodeDegree: 5 }
  },
  {
    id: 'companies-only',
    name: 'Companies Only',
    description: 'Focus on company entities',
    icon: <BuildingOffice2Icon className="w-4 h-4" />,
    filters: { entityTypes: ['company'], minNodeDegree: 0 }
  },
  {
    id: 'people-network',
    name: 'People Network',
    description: 'View contact relationships',
    icon: <UserIcon className="w-4 h-4" />,
    filters: { entityTypes: ['contact'], minNodeDegree: 0 }
  },
  {
    id: 'deals-flow',
    name: 'Deal Flow',
    description: 'Track deal relationships',
    icon: <CurrencyDollarIcon className="w-4 h-4" />,
    filters: { entityTypes: ['deal', 'company', 'contact'], minNodeDegree: 0 }
  },
  {
    id: 'full-network',
    name: 'Full Network',
    description: 'Show all entities and relationships',
    icon: <SparklesIcon className="w-4 h-4" />,
    filters: { entityTypes: ['company', 'contact', 'deal', 'project', 'estimate'], minNodeDegree: 0, linkTypes: [] }
  }
];

const KnowledgeGraph: React.FC = () => {
  const navigate = useNavigate();
  const fgRef = useRef<any>();
  const [hoveredNode, setHoveredNode] = useState<KnowledgeGraphNode | null>(null);
  const [selectedNode, setSelectedNode] = useState<string | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  
  // Check if knowledge graph feature is enabled
  const { enabled: featureEnabled, loading: featureLoading, error: featureError } = useFeatureFlag(FeatureFlag.KNOWLEDGE_GRAPH);
  const [graphDimensions, setGraphDimensions] = useState({ width: 800, height: 600 });
  const [isFilterLoading, setIsFilterLoading] = useState(false);
  const [pinnedNodes, setPinnedNodes] = useState<Set<string>>(new Set());
  const [showLabels, setShowLabels] = useState(true);
  const [lastDataUpdate, setLastDataUpdate] = useState<Date>(new Date());
  const [filterHistory, setFilterHistory] = useState<FilterHistoryItem[]>([]);
  const [showFilterHistory, setShowFilterHistory] = useState(false);
  const [contextMenu, setContextMenu] = useState<ContextMenuState>({
    visible: false,
    x: 0,
    y: 0,
    node: null
  });
  
  const [filters, setFilters] = useState<FilterState>({
    entityTypes: ['company', 'contact', 'deal', 'project'], // Removed 'estimate' - not in API default
    searchTerm: '',
    minNodeDegree: 0,
    linkTypes: []
  });
  
  // Debounce search term to avoid too many API calls
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(filters.searchTerm);
    }, 300); // 300ms delay
    
    return () => clearTimeout(timer);
  }, [filters.searchTerm]);

  // Build query string for API
  const buildQueryString = useCallback(() => {
    const params = new URLSearchParams();
    params.append('entityTypes', filters.entityTypes.join(','));
    params.append('maxNodes', '2000');
    
    if (debouncedSearchTerm) {
      params.append('searchTerm', debouncedSearchTerm);
    }
    
    if (filters.minNodeDegree > 0) {
      params.append('minNodeDegree', filters.minNodeDegree.toString());
    }
    
    if (filters.linkTypes.length > 0) {
      params.append('linkTypes', filters.linkTypes.join(','));
    }
    
    return params.toString();
  }, [filters.entityTypes, filters.minNodeDegree, filters.linkTypes, debouncedSearchTerm]);

  // Fetch knowledge graph data with all filters
  const { data: rawData, isLoading, error, refetch, isFetching } = useQuery<{ data: KnowledgeGraphData }>(
    ['knowledgeGraph', {
      entityTypes: filters.entityTypes,
      searchTerm: debouncedSearchTerm,
      minNodeDegree: filters.minNodeDegree,
      linkTypes: filters.linkTypes
    }], // Include all filters that affect server data
    () => fetchFromApi(`/api/knowledge-graph?${buildQueryString()}`),
    {
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
      enabled: filters.entityTypes.length > 0, // Don't fetch if no entity types selected
      onSettled: () => setIsFilterLoading(false),
      onSuccess: (data) => {
        setLastDataUpdate(new Date());
        // Add to filter history
        if (data?.data) {
          const historyItem: FilterHistoryItem = {
            id: Date.now().toString(),
            timestamp: Date.now(),
            filters: { ...filters },
            resultCount: data.data.nodes.length
          };
          setFilterHistory(prev => [
            historyItem,
            ...prev.filter((_, index) => index < 9) // Keep last 10 items
          ]);
        }
      }
    }
  );

  // Track when entity types change to show loading
  React.useEffect(() => {
    if (isFetching) {
      setIsFilterLoading(true);
    }
  }, [isFetching]);

  const graphData = rawData?.data;

  // Update graph dimensions on mount and resize
  useEffect(() => {
    const updateDimensions = () => {
      const container = document.getElementById('graph-container');
      if (container) {
        setGraphDimensions({
          width: container.clientWidth,
          height: container.clientHeight
        });
      }
    };

    updateDimensions();
    window.addEventListener('resize', updateDimensions);
    return () => window.removeEventListener('resize', updateDimensions);
  }, []);

  // Deep clone graph data to prevent ForceGraph mutations
  const graphDataForVisualization = useMemo(() => {
    if (!graphData) return { nodes: [], links: [] };

    // Deep clone the data to prevent ForceGraph from mutating our source
    return {
      nodes: graphData.nodes.map(node => ({ 
        ...node,
        // Apply pinning if node is pinned
        fx: pinnedNodes.has(node.id) ? node.x : undefined,
        fy: pinnedNodes.has(node.id) ? node.y : undefined
      })),
      links: graphData.links.map(link => ({ ...link }))
    };
  }, [graphData, pinnedNodes]);

  // Get available link types
  const availableLinkTypes = useMemo(() => {
    if (!graphData) return [];
    return Object.keys(graphData.stats.linkTypes).sort();
  }, [graphData]);

  // Calculate node degrees for sizing
  const nodeDegrees = useMemo(() => {
    const degrees = new Map<string, number>();
    if (!graphData) return degrees;
    
    graphData.links.forEach(link => {
      const sourceId = typeof link.source === 'object' ? link.source.id : link.source;
      const targetId = typeof link.target === 'object' ? link.target.id : link.target;
      degrees.set(sourceId, (degrees.get(sourceId) || 0) + 1);
      degrees.set(targetId, (degrees.get(targetId) || 0) + 1);
    });
    
    return degrees;
  }, [graphData]);

  // Get node color based on type
  const getNodeColor = useCallback((node: KnowledgeGraphNode): string => {
    const colors = {
      company: '#10B981', // Green
      contact: '#8B5CF6', // Purple
      deal: '#F59E0B', // Amber
      project: '#3B82F6', // Blue
      estimate: '#EC4899' // Pink
    };
    return colors[node.type] || '#6B7280';
  }, []);

  // Draw node icon using SVG paths
  const drawNodeIcon = useCallback((ctx: CanvasRenderingContext2D, x: number, y: number, type: string, size: number) => {
    ctx.save();
    ctx.translate(x, y);
    ctx.scale(size / 24, size / 24); // Icons are designed for 24x24
    ctx.fillStyle = '#FFFFFF';
    ctx.strokeStyle = '#FFFFFF';
    ctx.lineWidth = 1.5;
    
    switch (type) {
      case 'company':
        // Building icon
        ctx.beginPath();
        ctx.moveTo(-6, 8);
        ctx.lineTo(-6, -6);
        ctx.lineTo(0, -9);
        ctx.lineTo(6, -6);
        ctx.lineTo(6, 8);
        ctx.lineTo(-6, 8);
        ctx.closePath();
        ctx.fill();
        // Windows
        ctx.fillStyle = ctx.canvas.style.backgroundColor || '#1F2937';
        ctx.fillRect(-4, -4, 2, 2);
        ctx.fillRect(0, -4, 2, 2);
        ctx.fillRect(-4, 0, 2, 2);
        ctx.fillRect(0, 0, 2, 2);
        ctx.fillRect(-4, 4, 2, 2);
        ctx.fillRect(0, 4, 2, 2);
        break;
        
      case 'contact':
        // Person icon
        ctx.beginPath();
        ctx.arc(0, -3, 4, 0, Math.PI * 2);
        ctx.fill();
        ctx.beginPath();
        ctx.moveTo(-6, 8);
        ctx.quadraticCurveTo(-6, 2, -4, 2);
        ctx.lineTo(-2, 2);
        ctx.quadraticCurveTo(0, 0, 0, 0);
        ctx.quadraticCurveTo(0, 0, 2, 2);
        ctx.lineTo(4, 2);
        ctx.quadraticCurveTo(6, 2, 6, 8);
        ctx.closePath();
        ctx.fill();
        break;
        
      case 'deal':
        // Dollar sign in circle
        ctx.beginPath();
        ctx.arc(0, 0, 7, 0, Math.PI * 2);
        ctx.stroke();
        ctx.font = 'bold 10px sans-serif';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText('$', 0, 0);
        break;
        
      case 'project':
        // Folder icon
        ctx.beginPath();
        ctx.moveTo(-7, -3);
        ctx.lineTo(-7, 6);
        ctx.lineTo(7, 6);
        ctx.lineTo(7, -1);
        ctx.lineTo(2, -1);
        ctx.lineTo(0, -3);
        ctx.closePath();
        ctx.fill();
        break;
        
      case 'estimate':
        // Document icon
        ctx.beginPath();
        ctx.moveTo(-5, -8);
        ctx.lineTo(-5, 8);
        ctx.lineTo(5, 8);
        ctx.lineTo(5, -3);
        ctx.lineTo(0, -8);
        ctx.closePath();
        ctx.fill();
        // Page fold
        ctx.beginPath();
        ctx.moveTo(0, -8);
        ctx.lineTo(0, -3);
        ctx.lineTo(5, -3);
        ctx.closePath();
        ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
        ctx.fill();
        break;
        
      default:
        // Generic circle
        ctx.beginPath();
        ctx.arc(0, 0, 5, 0, Math.PI * 2);
        ctx.fill();
    }
    
    ctx.restore();
  }, []);

  // Apply filter preset
  const applyFilterPreset = useCallback((preset: FilterPreset) => {
    setFilters(prev => ({
      ...prev,
      ...preset.filters
    }));
  }, []);

  // Handle context menu
  const handleNodeRightClick = useCallback((node: KnowledgeGraphNode, event: MouseEvent) => {
    event.preventDefault();
    setContextMenu({
      visible: true,
      x: event.clientX,
      y: event.clientY,
      node
    });
  }, []);

  // Hide context menu when clicking elsewhere
  useEffect(() => {
    const handleClick = () => setContextMenu(prev => ({ ...prev, visible: false }));
    document.addEventListener('click', handleClick);
    return () => document.removeEventListener('click', handleClick);
  }, []);

  // Toggle node pinning
  const toggleNodePin = useCallback((nodeId: string) => {
    setPinnedNodes(prev => {
      const newSet = new Set(prev);
      if (newSet.has(nodeId)) {
        newSet.delete(nodeId);
      } else {
        // Get current node position to pin it
        const node = graphDataForVisualization.nodes.find(n => n.id === nodeId);
        if (node && fgRef.current) {
          const coords = fgRef.current.graph2ScreenCoords(node.x, node.y);
          node.fx = node.x;
          node.fy = node.y;
        }
        newSet.add(nodeId);
      }
      return newSet;
    });
  }, [graphDataForVisualization]);

  // Check if text matches search term
  const matchesSearchTerm = useCallback((text: string): boolean => {
    if (!filters.searchTerm) return false;
    return text.toLowerCase().includes(filters.searchTerm.toLowerCase());
  }, [filters.searchTerm]);

  // Handle node click
  const handleNodeClick = useCallback((node: KnowledgeGraphNode) => {
    setSelectedNode(node.id);
    
    // Navigate to entity detail
    switch (node.type) {
      case 'company':
        navigate(`/crm/companies?selected=${node.id}`);
        break;
      case 'contact':
        navigate(`/crm/contacts?selected=${node.id}`);
        break;
      case 'deal':
        navigate(`/crm/deals/${node.id}`);
        break;
      case 'project':
        // Projects don't have a detail page yet
        console.log('Project clicked:', node);
        break;
      case 'estimate':
        navigate(`/estimates?selected=${node.id}`);
        break;
    }
  }, [navigate]);

  // Draw custom node
  const drawNode = useCallback((node: KnowledgeGraphNode, ctx: CanvasRenderingContext2D, globalScale: number) => {
    const label = node.label;
    const fontSize = 12 / globalScale;
    const isHovered = hoveredNode?.id === node.id;
    const isSelected = selectedNode === node.id;
    const isPinned = pinnedNodes.has(node.id);
    const matchesSearch = matchesSearchTerm(label);
    const degree = nodeDegrees.get(node.id) || 0;
    
    // Calculate node size based on degree (connections)
    const baseSize = 6;
    const nodeSize = baseSize + Math.min(degree * 0.5, 12); // Max size of 18
    
    // Draw node circle
    ctx.beginPath();
    const radius = nodeSize * (isHovered ? 1.3 : 1) * (isSelected ? 1.2 : 1);
    ctx.arc(node.x || 0, node.y || 0, radius, 0, 2 * Math.PI, false);
    
    // Fill with gradient
    const gradient = ctx.createRadialGradient(
      node.x || 0, node.y || 0, 0,
      node.x || 0, node.y || 0, radius
    );
    const color = getNodeColor(node);
    gradient.addColorStop(0, color + 'FF');
    gradient.addColorStop(1, color + '88');
    ctx.fillStyle = gradient;
    ctx.fill();

    // Draw border
    if (isSelected || isHovered || matchesSearch) {
      ctx.strokeStyle = matchesSearch ? '#DC2626' : (isSelected ? '#3B82F6' : '#9CA3AF');
      ctx.lineWidth = matchesSearch ? 3 : (isSelected ? 3 : 2);
      ctx.stroke();
    }

    // Draw pinned indicator
    if (isPinned) {
      ctx.save();
      const pinSize = 10 / globalScale;
      ctx.translate(node.x || 0, (node.y || 0) - radius - pinSize);
      
      // Draw pin icon
      ctx.fillStyle = '#3B82F6';
      ctx.strokeStyle = '#3B82F6';
      ctx.lineWidth = 1;
      
      // Pin body
      ctx.beginPath();
      ctx.arc(0, -pinSize/2, pinSize/3, 0, Math.PI * 2);
      ctx.fill();
      
      // Pin needle
      ctx.beginPath();
      ctx.moveTo(0, -pinSize/2);
      ctx.lineTo(0, pinSize/2);
      ctx.stroke();
      
      // Pin head
      ctx.beginPath();
      ctx.arc(0, -pinSize/2, pinSize/5, 0, Math.PI * 2);
      ctx.fillStyle = '#FFFFFF';
      ctx.fill();
      ctx.stroke();
      
      ctx.restore();
    }

    // Draw icon
    const iconSize = radius * 1.2;
    drawNodeIcon(ctx, node.x || 0, node.y || 0, node.type, iconSize);

    // Draw label
    if (showLabels && globalScale > 0.5) {
      ctx.font = `${fontSize}px sans-serif`;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'top';
      
      // Background for label
      const labelWidth = ctx.measureText(label).width;
      ctx.fillStyle = matchesSearch ? 'rgba(254, 202, 202, 0.95)' : 'rgba(255, 255, 255, 0.9)';
      ctx.fillRect(
        (node.x || 0) - labelWidth / 2 - 2,
        (node.y || 0) + radius + 2,
        labelWidth + 4,
        fontSize + 4
      );
      
      // Draw text
      ctx.fillStyle = matchesSearch ? '#991B1B' : '#1F2937';
      ctx.fillText(label, node.x || 0, (node.y || 0) + radius + 4);
    }
  }, [hoveredNode, selectedNode, getNodeColor, drawNodeIcon, pinnedNodes, matchesSearchTerm, nodeDegrees, showLabels]);

  // Draw custom link
  const drawLink = useCallback((link: any, ctx: CanvasRenderingContext2D, globalScale: number) => {
    // Draw link with thickness based on strength
    const strength = link.strength || 1;
    ctx.lineWidth = Math.max(0.5, strength * 0.3) / globalScale;
    
    // Draw link label if exists and scale is appropriate
    if (link.label && globalScale > 0.7) {
      const midX = (link.source.x + link.target.x) / 2;
      const midY = (link.source.y + link.target.y) / 2;
      
      ctx.font = `${10 / globalScale}px sans-serif`;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      
      // Background for label
      const labelWidth = ctx.measureText(link.label).width;
      ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
      ctx.fillRect(
        midX - labelWidth / 2 - 2,
        midY - 6,
        labelWidth + 4,
        12
      );
      
      ctx.fillStyle = '#6B7280';
      ctx.fillText(link.label, midX, midY);
    }
  }, []);

  // Export graph as image
  const exportGraph = useCallback(() => {
    if (!fgRef.current) return;
    
    const canvas = fgRef.current.scene().context().canvas as HTMLCanvasElement;
    const dataURL = canvas.toDataURL('image/png');
    
    const link = document.createElement('a');
    link.download = `knowledge-graph-${new Date().toISOString().split('T')[0]}.png`;
    link.href = dataURL;
    link.click();
  }, []);

  // Center graph
  const centerGraph = useCallback(() => {
    if (fgRef.current) {
      fgRef.current.zoomToFit(400);
    }
  }, []);

  // Show loading while checking feature flag
  if (featureLoading) {
    return (
      <Card>
        <div className="flex items-center justify-center h-96">
          <div className="text-gray-500">Checking feature availability...</div>
        </div>
      </Card>
    );
  }

  // Show disabled message if feature is not enabled
  if (!featureEnabled) {
    return (
      <Card>
        <div className="flex flex-col items-center justify-center h-96 space-y-4">
          <div className="text-center">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              Knowledge Graph Feature Disabled
            </h3>
            <p className="text-gray-500 dark:text-gray-400">
              The Knowledge Graph feature is currently disabled. Please contact your administrator to enable this feature.
            </p>
          </div>
        </div>
      </Card>
    );
  }

  if (isLoading && !graphData) {
    return (
      <Card>
        <div className="flex items-center justify-center h-96">
          <div className="text-gray-500">Loading knowledge graph...</div>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <div className="flex items-center justify-center h-96">
          <div className="text-red-500">
            Failed to load knowledge graph
            {process.env.NODE_ENV === 'development' && (
              <div className="text-xs mt-2">{(error as any)?.message || 'Unknown error'}</div>
            )}
          </div>
        </div>
      </Card>
    );
  }

  return (
    <div className="knowledge-graph h-full flex flex-col p-2">
      {/* Compact Header */}
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-3">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Knowledge Graph</h2>
          <p className="text-xs text-gray-500 dark:text-gray-400">
            {graphData?.nodes.length || 0} entities • {graphData?.links.length || 0} relationships
            {lastDataUpdate && (
              <span className="ml-2">
                <ClockIcon className="w-3 h-3 inline mr-0.5" />
                {new Date().getTime() - lastDataUpdate.getTime() < 60000 
                  ? 'now' 
                  : `${Math.floor((new Date().getTime() - lastDataUpdate.getTime()) / 60000)}m`}
              </span>
            )}
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          {/* Compact View Controls */}
          <button
            onClick={() => setShowLabels(!showLabels)}
            className={`p-1.5 rounded-md transition-all ${
              showLabels
                ? 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300'
                : 'text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-900'
            }`}
            title={showLabels ? "Hide labels" : "Show labels"}
          >
            {showLabels ? <EyeIcon className="w-4 h-4" /> : <EyeSlashIcon className="w-4 h-4" />}
          </button>
          
          <button
            onClick={() => setShowFilterHistory(!showFilterHistory)}
            className="p-1.5 text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-900 rounded-md transition-all relative"
            title="Filter history"
          >
            <ClockIcon className="w-4 h-4" />
            {filterHistory.length > 0 && (
              <span className="absolute -top-1 -right-1 w-1.5 h-1.5 bg-purple-500 rounded-full"></span>
            )}
          </button>
          
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`p-1.5 rounded-md transition-all flex items-center gap-1 ${
              showFilters 
                ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400'
                : 'text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-900'
            }`}
          >
            <AdjustmentsHorizontalIcon className="w-4 h-4" />
            <span className="text-xs">Filters</span>
          </button>
          
          <button
            onClick={centerGraph}
            className="p-1.5 text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-900 rounded-md transition-all"
            title="Center graph"
          >
            <ArrowsPointingInIcon className="w-4 h-4" />
          </button>
          
          <button
            onClick={exportGraph}
            className="p-1.5 text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-900 rounded-md transition-all"
            title="Export as image"
          >
            <ArrowDownTrayIcon className="w-4 h-4" />
          </button>
        </div>
      </div>

      <div className="flex-1 flex gap-2 min-h-0">
        {/* Filter Panel */}
        {showFilters && (
          <Card className="w-72 h-full overflow-y-auto flex-shrink-0">
            <div className="p-3">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-semibold text-gray-900 dark:text-white">Filters</h3>
                <button
                  onClick={() => setShowFilters(false)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <XMarkIcon className="w-5 h-5" />
                </button>
              </div>
              
              {/* Filter Presets */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Quick Presets
                </label>
                <div className="grid grid-cols-2 gap-2">
                  {FILTER_PRESETS.map(preset => (
                    <button
                      key={preset.id}
                      onClick={() => applyFilterPreset(preset)}
                      className="p-2 text-left rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-all group"
                      title={preset.description}
                    >
                      <div className="flex items-start gap-2">
                        <div className="text-gray-500 dark:text-gray-400 group-hover:text-purple-600 dark:group-hover:text-purple-400">
                          {preset.icon}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="text-xs font-medium text-gray-900 dark:text-white truncate">
                            {preset.name}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400 truncate">
                            {preset.description}
                          </div>
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              </div>
              
              {/* Search */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Search
                </label>
                <div className="relative">
                  <input
                    type="text"
                    value={filters.searchTerm}
                    onChange={(e) => setFilters(prev => ({ ...prev, searchTerm: e.target.value }))}
                    placeholder="Search entities..."
                    className="w-full pl-9 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white text-sm"
                  />
                  <MagnifyingGlassIcon className="absolute left-3 top-2.5 w-4 h-4 text-gray-400" />
                </div>
              </div>
              
              {/* Entity Types */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Entity Types
                </label>
                <div className="space-y-2">
                  {['company', 'contact', 'deal', 'project', 'estimate'].map(type => (
                    <label key={type} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={filters.entityTypes.includes(type)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setFilters(prev => ({
                              ...prev,
                              entityTypes: [...prev.entityTypes, type]
                            }));
                          } else {
                            setFilters(prev => ({
                              ...prev,
                              entityTypes: prev.entityTypes.filter(t => t !== type)
                            }));
                          }
                        }}
                        className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 dark:border-gray-600 rounded"
                      />
                      <span className="ml-2 text-sm text-gray-700 dark:text-gray-300 capitalize flex items-center gap-1">
                        {type === 'company' && <BuildingOffice2Icon className="w-4 h-4" />}
                        {type === 'contact' && <UserIcon className="w-4 h-4" />}
                        {type === 'deal' && <CurrencyDollarIcon className="w-4 h-4" />}
                        {type === 'project' && <ClipboardDocumentCheckIcon className="w-4 h-4" />}
                        {type === 'estimate' && <DocumentTextIcon className="w-4 h-4" />}
                        {type}s
                        {graphData && (
                          <span className="text-gray-500 ml-1">
                            ({graphData.stats.nodeTypes[type] || 0})
                          </span>
                        )}
                      </span>
                    </label>
                  ))}
                </div>
              </div>
              
              {/* Link Types */}
              {availableLinkTypes.length > 0 && (
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Relationship Types
                  </label>
                  <div className="space-y-2 max-h-40 overflow-y-auto">
                    {availableLinkTypes.map(type => (
                      <label key={type} className="flex items-center">
                        <input
                          type="checkbox"
                          checked={filters.linkTypes.includes(type)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setFilters(prev => ({
                                ...prev,
                                linkTypes: [...prev.linkTypes, type]
                              }));
                            } else {
                              setFilters(prev => ({
                                ...prev,
                                linkTypes: prev.linkTypes.filter(t => t !== type)
                              }));
                            }
                          }}
                          className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 dark:border-gray-600 rounded"
                        />
                        <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                          {type.replace(/_/g, ' ')}
                          {graphData && (
                            <span className="text-gray-500 ml-1">
                              ({graphData.stats.linkTypes[type] || 0})
                            </span>
                          )}
                        </span>
                      </label>
                    ))}
                  </div>
                </div>
              )}
              
              {/* Minimum Connections */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Minimum Connections
                </label>
                <input
                  type="range"
                  min="0"
                  max="10"
                  value={filters.minNodeDegree}
                  onChange={(e) => setFilters(prev => ({ 
                    ...prev, 
                    minNodeDegree: parseInt(e.target.value) 
                  }))}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>0</span>
                  <span>{filters.minNodeDegree}</span>
                  <span>10+</span>
                </div>
              </div>
              
              {/* Reset Filters */}
              <button
                onClick={() => setFilters({
                  entityTypes: ['company', 'contact', 'deal', 'project'],
                  searchTerm: '',
                  minNodeDegree: 0,
                  linkTypes: []
                })}
                className="w-full text-sm text-purple-600 hover:text-purple-700 dark:text-purple-400 dark:hover:text-purple-300"
              >
                Reset filters
              </button>
            </div>
          </Card>
        )}
        
        {/* Filter History Panel */}
        {showFilterHistory && (
          <Card className="w-72 h-full overflow-y-auto flex-shrink-0">
            <div className="p-3">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-semibold text-gray-900 dark:text-white">Filter History</h3>
                <button
                  onClick={() => setShowFilterHistory(false)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <XMarkIcon className="w-5 h-5" />
                </button>
              </div>
              
              {filterHistory.length === 0 ? (
                <p className="text-sm text-gray-500 dark:text-gray-400">No filter history yet</p>
              ) : (
                <div className="space-y-2 max-h-96 overflow-y-auto">
                  {filterHistory.map((item) => (
                    <button
                      key={item.id}
                      onClick={() => setFilters(item.filters)}
                      className="w-full p-3 text-left rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-all"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {item.resultCount} results
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            {item.filters.entityTypes.join(', ')}
                            {item.filters.searchTerm && ` • "${item.filters.searchTerm}"`}
                            {item.filters.minNodeDegree > 0 && ` • ${item.filters.minNodeDegree}+ connections`}
                          </div>
                        </div>
                        <div className="text-xs text-gray-400 dark:text-gray-500 ml-2">
                          {new Date(item.timestamp).toLocaleTimeString()}
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              )}
              
              {filterHistory.length > 0 && (
                <button
                  onClick={() => setFilterHistory([])}
                  className="w-full mt-3 text-sm text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                >
                  Clear history
                </button>
              )}
            </div>
          </Card>
        )}
        
        {/* Graph Container */}
        <Card className="flex-1 min-w-0">
          <div id="graph-container" className="w-full h-full relative overflow-hidden">
            {/* Loading overlay */}
            {isFilterLoading && (
              <div className="absolute inset-0 bg-white/80 dark:bg-gray-900/80 z-10 flex items-center justify-center">
                <div className="text-gray-500">Updating graph...</div>
              </div>
            )}
            <ForceGraph2D
              ref={fgRef}
              graphData={graphDataForVisualization}
              nodeCanvasObject={drawNode}
              nodeCanvasObjectMode={() => 'replace'}
              linkCanvasObjectMode={() => 'after'}
              linkCanvasObject={drawLink}
              onNodeClick={handleNodeClick}
              onNodeRightClick={handleNodeRightClick}
              onNodeHover={setHoveredNode}
              onNodeDragEnd={(node) => {
                if (pinnedNodes.has(node.id)) {
                  node.fx = node.x;
                  node.fy = node.y;
                }
              }}
              cooldownTicks={100}
              nodeRelSize={1}
              linkDirectionalArrowLength={3}
              linkDirectionalArrowRelPos={1}
              linkCurvature={0.1}
              enableNodeDrag={true}
              enableZoomInteraction={true}
              enablePanInteraction={true}
              width={graphDimensions.width}
              height={graphDimensions.height}
              backgroundColor="rgba(0,0,0,0)"
            />
            
            {/* Compact Hover tooltip */}
            {hoveredNode && (
              <div className="absolute top-2 right-2 bg-white dark:bg-gray-800 rounded-md shadow-lg p-2 max-w-xs pointer-events-none">
                <div className="flex items-center gap-1.5 mb-1">
                  {hoveredNode.type === 'company' && <BuildingOffice2Icon className="w-3 h-3 text-gray-500" />}
                  {hoveredNode.type === 'contact' && <UserIcon className="w-3 h-3 text-gray-500" />}
                  {hoveredNode.type === 'deal' && <CurrencyDollarIcon className="w-3 h-3 text-gray-500" />}
                  {hoveredNode.type === 'project' && <ClipboardDocumentCheckIcon className="w-3 h-3 text-gray-500" />}
                  {hoveredNode.type === 'estimate' && <DocumentTextIcon className="w-3 h-3 text-gray-500" />}
                  <span className="text-xs text-gray-500 dark:text-gray-400 capitalize">{hoveredNode.type}</span>
                </div>
                <div className="text-sm font-medium text-gray-900 dark:text-white">{hoveredNode.label}</div>
                {hoveredNode.metadata && (
                  <div className="text-xs text-gray-500 dark:text-gray-400 mt-0.5">
                    {hoveredNode.type === 'company' && hoveredNode.metadata.industry && (
                      <div>{hoveredNode.metadata.industry}</div>
                    )}
                    {hoveredNode.type === 'contact' && hoveredNode.metadata.email && (
                      <div>{hoveredNode.metadata.email}</div>
                    )}
                    {hoveredNode.type === 'deal' && hoveredNode.metadata.value && (
                      <div>${hoveredNode.metadata.value.toLocaleString()}</div>
                    )}
                    {hoveredNode.type === 'project' && hoveredNode.metadata.status && (
                      <div>{hoveredNode.metadata.status}</div>
                    )}
                  </div>
                )}
                <div className="text-xs text-gray-400 dark:text-gray-500 mt-1">Click to view</div>
              </div>
            )}
            
            {/* Compact Legend */}
            <div className="absolute bottom-2 left-2 bg-white/90 dark:bg-gray-800/90 rounded-md shadow-md p-2">
              <div className="flex items-center gap-3 text-xs">
                <div className="flex items-center gap-1">
                  <div className="w-2.5 h-2.5 rounded-full bg-green-500"></div>
                  <span className="text-gray-600 dark:text-gray-400">Company</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-2.5 h-2.5 rounded-full bg-purple-500"></div>
                  <span className="text-gray-600 dark:text-gray-400">Contact</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-2.5 h-2.5 rounded-full bg-amber-500"></div>
                  <span className="text-gray-600 dark:text-gray-400">Deal</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-2.5 h-2.5 rounded-full bg-blue-500"></div>
                  <span className="text-gray-600 dark:text-gray-400">Project</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-2.5 h-2.5 rounded-full bg-pink-500"></div>
                  <span className="text-gray-600 dark:text-gray-400">Estimate</span>
                </div>
              </div>
            </div>
            
            {/* Context Menu */}
            {contextMenu.visible && contextMenu.node && (
              <div
                className="absolute bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 py-1 z-50"
                style={{ left: contextMenu.x, top: contextMenu.y }}
                onContextMenu={(e) => e.preventDefault()}
              >
                <button
                  onClick={() => {
                    if (contextMenu.node) {
                      handleNodeClick(contextMenu.node);
                    }
                    setContextMenu({ visible: false, x: 0, y: 0, node: null });
                  }}
                  className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-2"
                >
                  <EyeIcon className="w-4 h-4" />
                  View Details
                </button>
                <button
                  onClick={() => {
                    if (contextMenu.node) {
                      toggleNodePin(contextMenu.node.id);
                    }
                    setContextMenu({ visible: false, x: 0, y: 0, node: null });
                  }}
                  className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-2"
                >
                  {contextMenu.node && pinnedNodes.has(contextMenu.node.id) ? (
                    <>
                      <MapPinIconSolid className="w-4 h-4" />
                      Unpin Node
                    </>
                  ) : (
                    <>
                      <MapPinIcon className="w-4 h-4 text-gray-400" />
                      Pin Node
                    </>
                  )}
                </button>
                <button
                  onClick={() => {
                    if (contextMenu.node) {
                      centerGraph();
                      if (fgRef.current) {
                        fgRef.current.centerAt(contextMenu.node.x, contextMenu.node.y, 500);
                      }
                    }
                    setContextMenu({ visible: false, x: 0, y: 0, node: null });
                  }}
                  className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-2"
                >
                  <ArrowsPointingInIcon className="w-4 h-4" />
                  Focus on Node
                </button>
                <div className="border-t border-gray-200 dark:border-gray-700 my-1"></div>
                <button
                  onClick={() => {
                    if (contextMenu.node) {
                      navigator.clipboard.writeText(contextMenu.node.label);
                    }
                    setContextMenu({ visible: false, x: 0, y: 0, node: null });
                  }}
                  className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-2"
                >
                  <ShareIcon className="w-4 h-4" />
                  Copy Name
                </button>
              </div>
            )}
          </div>
        </Card>
      </div>
    </div>
  );
};

export default KnowledgeGraph;