/**
 * @deprecated This file has been consolidated into utils/colors.ts
 * Import from utils/colors.ts instead
 */

// For now, keep the old implementation to avoid breaking changes
export const paygwColors = {
  normal: '#3B82F6',    // Blue
  bg_normal: '#EFF6FF',
  important: '#F59E0B', // Orange
  bg_important: '#FEF3C7',
  urgent: '#EF4444',    // Red
  bg_urgent: '#FEE2E2'
};

export const gstColors = {
  normal: '#10B981',    // Green
  bg_normal: '#ECFDF5',
  important: '#F59E0B', // Orange
  bg_important: '#FEF3C7', 
  urgent: '#EF4444',    // Red
  bg_urgent: '#FEE2E2'
};

export const getColorClass = (
  type: 'paygw' | 'gst',
  importance: 'normal' | 'important' | 'urgent',
  background: boolean = false
): string => {
  const colors = type === 'paygw' ? paygwColors : gstColors;
  const key = background ? `bg_${importance}` : importance;
  return colors[key as keyof typeof colors];
};
