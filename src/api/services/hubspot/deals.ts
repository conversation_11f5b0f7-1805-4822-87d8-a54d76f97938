import { Client } from '@hubspot/api-client';
import { DealRepository } from '../../repositories/deal-repository';
import { CompanyRepository } from '../../repositories/company-repository';
import { HubSpotProgressTracker, ImportResult } from './progress-tracker';
import { HubSpotUtils } from './utils';
import { DealStage } from '../../../frontend/types/crm-types';
import activityLogger from '../../../utils/activity-logger';

// Define types for HubSpot objects
interface HubSpotObject {
  id: string;
  properties: {
    [key: string]: string | undefined;
  };
}

/**
 * HubSpot deals import functionality
 */
export class HubSpotDeals {
  private dealRepository: DealRepository;
  private companyRepository: CompanyRepository;
  private progressTracker: HubSpotProgressTracker;

  constructor(progressTracker: HubSpotProgressTracker) {
    this.dealRepository = new DealRepository();
    this.companyRepository = new CompanyRepository();
    this.progressTracker = progressTracker;
  }

  /**
   * Get deal-company association from HubSpot
   */
  private async getDealCompanyAssociation(client: Client, dealId: string): Promise<string | null> {
    try {
      // Check if the associationsApi exists, if not use the basic API
      if (!client.crm?.deals?.associationsApi) {
        console.warn('AssociationsApi not available, using basic API to fetch deal with associations');
        
        // Fetch the deal with associations included
        const deal = await client.crm.deals.basicApi.getById(dealId, undefined, undefined, ['companies']);
        
        if (deal.associations?.companies?.results && deal.associations.companies.results.length > 0) {
          return deal.associations.companies.results[0].id;
        }
        
        return null;
      }

      const associations = await client.crm.deals.associationsApi.getAll(
        dealId,
        'companies'
      );

      if (associations.results && associations.results.length > 0) {
        return associations.results[0].id;
      }

      return null;
    } catch (error) {
      console.error(`Error getting company association for deal ${dealId}:`, error);
      return null;
    }
  }

  /**
   * Map HubSpot deal stages to our internal stages
   */
  private mapDealStage(hubspotStage: string): DealStage {
    const stageMapping: Record<string, DealStage> = {
      // Numeric stage IDs from HubSpot
      '*********': 'Identified',
      '*********': 'Qualified',
      '*********': 'Solution proposal',
      '*********': 'Solution presentation',
      '*********': 'Objection handling',
      '*********': 'Finalising terms',
      '*********': 'Closed won',
      '*********': 'Closed lost',
      '*********': 'Abandoned'
    };

    return stageMapping[hubspotStage] || stageMapping[hubspotStage.toLowerCase()] || 'Identified';
  }

  /**
   * Parse probability from HubSpot chance_of_win field
   */
  private parseProbability(chanceOfWin: string | undefined): number | undefined {
    if (!chanceOfWin) return undefined;

    const parsedProb = parseFloat(chanceOfWin);
    if (isNaN(parsedProb)) return undefined;

    // HubSpot stores chance_of_win as a decimal (e.g., 0.95 for 95%)
    // Ensure it's within the valid range of 0-1
    return Math.min(1, Math.max(0, parsedProb));
  }

  /**
   * Extract custom fields from HubSpot deal properties
   */
  private extractCustomFields(properties: { [key: string]: string | undefined }): Record<string, any> {
    const standardFields = [
      'dealname', 'dealstage', 'amount', 'closedate', 'description', 'hubspot_owner_id', 'chance_of_win',
      'createdate', 'hs_lastmodifieddate' // Exclude these date fields from custom fields
    ];

    const customFields: Record<string, any> = {};
    for (const [key, value] of Object.entries(properties)) {
      if (!standardFields.includes(key) && value !== null && value !== undefined && value !== '') {
        customFields[key] = value;
      }
    }

    return customFields;
  }

  /**
   * Import deals from HubSpot with progress tracking
   */
  async importDeals(client: Client): Promise<ImportResult> {
    const errors: Array<{ item: string; error: string }> = [];
    const updates: Array<{ item: string; changes: string[] }> = [];
    const created: Array<{ item: string }> = [];

    // Log sync start activity
    activityLogger.logHubSpotSyncStarted().catch(error => {
      console.error('Error logging HubSpot sync start activity:', error);
    });

    try {
      // Get deals from HubSpot using the search API with pagination
      const properties = [
        'dealname',
        'dealstage',
        'amount',
        'closedate',
        'pipeline',
        'description',
        'hubspot_owner_id',
        'createdate',
        'hs_lastmodifieddate',
        'chance_of_win' // Custom HubSpot field for probability
      ];

      let allDeals: HubSpotObject[] = [];
      let hasMore = true;
      let after = 0;
      const pageSize = 100;
      let totalFound = 0;

      console.log('Fetching deals from HubSpot...');

      // Implement pagination to fetch all deals
      while (hasMore) {
        const publicObjectSearchRequest = {
          filterGroups: [],
          properties,
          limit: pageSize,
          after: after ? after.toString() : undefined
        };

        const response = await HubSpotUtils.withRetry(
          () => client.crm.deals.searchApi.doSearch(publicObjectSearchRequest)
        );

        allDeals = [...allDeals, ...response.results];
        totalFound += response.results.length;

        console.log(`Fetched page of ${response.results.length} deals. Total so far: ${totalFound}`);

        if (response.paging && response.paging.next && response.paging.next.after) {
          after = parseInt(response.paging.next.after);
        } else {
          hasMore = false;
        }
      }

      console.log(`Found ${allDeals.length} deals in HubSpot`);

      // Count deals by stage for debugging
      const stageCount: Record<string, number> = {};
      allDeals.forEach(deal => {
        const stage = deal.properties.dealstage;
        stageCount[stage || 'Unknown'] = (stageCount[stage || 'Unknown'] || 0) + 1;
      });

      console.log('HubSpot deal stages distribution:');
      Object.entries(stageCount).forEach(([stage, count]) => {
        console.log(`- Stage "${stage}": ${count} deals`);
      });

      if (allDeals.length > 0) {
        console.log('Sample deal data from HubSpot:');
        allDeals.slice(0, 3).forEach(deal => {
          console.log(`- Deal "${deal.properties.dealname}": Stage = "${deal.properties.dealstage}", Chance of Win = "${deal.properties.chance_of_win || 'Not set'}"`);
        });
      }

      // Start progress tracking
      this.progressTracker.startStep('deals', allDeals.length);

      let importedCount = 0;

      // Process each deal
      for (let i = 0; i < allDeals.length; i++) {
        const deal = allDeals[i];
        const dealName = deal.properties.dealname || `Deal ${deal.id}`;

        this.progressTracker.updateProgress(i + 1, `Processing deal: ${dealName}`);

        try {
          const hubspotId = deal.id;
          const hubspotStage = deal.properties.dealstage || '';
          const mappedStage = this.mapDealStage(hubspotStage);

          console.log(`Mapping HubSpot stage "${hubspotStage}" to "${mappedStage}" for deal "${dealName}" (HubSpot ID: ${hubspotId})`);

          // Check if the deal already exists by HubSpot ID
          let existingDeal = null;
          try {
            existingDeal = this.dealRepository.getDealByHubspotId(hubspotId);
          } catch (error) {
            console.log('Note: Could not check for existing deal by HubSpot ID, will create new deal');
          }

          if (existingDeal) {
            // Update existing deal
            console.log(`Updating existing deal "${existingDeal.name}" with HubSpot ID ${hubspotId}`);

            const probability = this.parseProbability(deal.properties.chance_of_win);
            const customFields = this.extractCustomFields(deal.properties);

            // Merge with existing custom fields
            const mergedCustomFields = {
              ...(existingDeal.customFields || {}),
              ...customFields
            };

            // Import field ownership check
            const { getFieldOwner } = require('../../../utils/deal-tracking');
            
            // Check if name is controlled by estimate
            const nameOwner = getFieldOwner(existingDeal.id, 'name');
            const valueOwner = getFieldOwner(existingDeal.id, 'value');
            
            const newData: any = {
              stage: mappedStage,
              currency: 'AUD',
              probability: probability,
              expectedCloseDate: deal.properties.closedate,
              description: HubSpotUtils.sanitizePropertyValue(deal.properties.description),
              owner: HubSpotUtils.sanitizePropertyValue(deal.properties.hubspot_owner_id),
              // Always update HubSpot preserved fields
              hubspot_name: dealName,
              hubspot_value: HubSpotUtils.formatCurrency(deal.properties.amount)
            };
            
            // Only update name if not controlled by estimate
            if (!nameOwner || nameOwner === 'hubspot') {
              newData.name = dealName;
            }
            
            // Only update value if not controlled by estimate
            if (!valueOwner || valueOwner === 'hubspot') {
              newData.value = HubSpotUtils.formatCurrency(deal.properties.amount);
            }

            const fieldsToTrack = ['name', 'stage', 'value', 'probability', 'expectedCloseDate', 'description', 'owner'];
            const changes = HubSpotUtils.trackChanges(existingDeal, newData, fieldsToTrack);

            const updatedDeal = this.dealRepository.updateDeal(
              existingDeal.id,
              {
                ...newData,
                customFields: mergedCustomFields
              },
              'HubSpot'
            );

            if (updatedDeal) {
              importedCount++;
              // Only track the update if there were actual changes
              if (changes.length > 0) {
                updates.push({
                  item: dealName,
                  changes: changes
                });
              }
              
              // Sync deal name to linked estimates if name changed
              if (newData.name && existingDeal.name !== newData.name) {
                const { updateEstimateFromDeal } = require('../../../utils/estimate-deal-sync');
                const { DealEstimateRepository } = require('../../repositories/relationships/deal-estimate-repository');
                const dealEstimateRepo = new DealEstimateRepository();
                const linkedEstimates = dealEstimateRepo.getEstimatesForDeal(existingDeal.id);
                
                for (const est of linkedEstimates.filter(e => e.estimateType === 'internal')) {
                  updateEstimateFromDeal(existingDeal.id, est.estimateId).catch(err => {
                    console.error(`Error syncing deal name to estimate ${est.estimateId}:`, err);
                  });
                }
              }
            }
          } else {
            // Create new deal
            console.log(`Creating new deal "${dealName}" with HubSpot ID ${hubspotId}`);

            const probability = this.parseProbability(deal.properties.chance_of_win);
            if (probability !== undefined) {
              console.log(`Parsed probability for new deal "${dealName}": ${probability * 100}% (from HubSpot value: ${deal.properties.chance_of_win})`);
            }

            const customFields = this.extractCustomFields(deal.properties);

            // Get the associated company from HubSpot
            let companyId: string | null = null;

            try {
              const hubspotCompanyId = await this.getDealCompanyAssociation(client, hubspotId);

              if (!hubspotCompanyId) {
                const errorMsg = `No company association found in HubSpot. Please associate this deal with a company in HubSpot.`;
                console.error(`Skipping deal "${dealName}" - ${errorMsg}`);
                this.progressTracker.addError(dealName, errorMsg);
                errors.push({
                  item: dealName,
                  error: errorMsg
                });
                continue;
              }

              // Try to find the company in our database by HubSpot ID
              const existingCompany = this.companyRepository.getCompanyByHubspotId(hubspotCompanyId);

              if (!existingCompany) {
                const errorMsg = `Company not found in database. Please import companies first.`;
                console.error(`Skipping deal "${dealName}" - company with HubSpot ID ${hubspotCompanyId} not found in database.`);
                this.progressTracker.addError(dealName, errorMsg);
                errors.push({
                  item: dealName,
                  error: errorMsg
                });
                continue;
              }

              companyId = existingCompany.id;
              console.log(`Using company "${existingCompany.name}" (ID: ${companyId}) for deal "${dealName}"`);

            } catch (error) {
              const errorMsg = `Unable to get company association: ${error instanceof Error ? error.message : 'Unknown error'}`;
              console.error(`Error getting company association for deal "${dealName}":`, error);
              this.progressTracker.addError(dealName, errorMsg);
              errors.push({
                item: dealName,
                error: errorMsg
              });
              continue;
            }

            if (!companyId) {
              const errorMsg = `No company ID available for deal`;
              console.error(`Skipping deal "${dealName}" - ${errorMsg}`);
              this.progressTracker.addError(dealName, errorMsg);
              errors.push({
                item: dealName,
                error: errorMsg
              });
              continue;
            }

            // Create the deal
            const dealData = {
              name: dealName,
              stage: mappedStage,
              value: HubSpotUtils.formatCurrency(deal.properties.amount),
              currency: 'AUD',
              probability: probability,
              expectedCloseDate: deal.properties.closedate,
              description: HubSpotUtils.sanitizePropertyValue(deal.properties.description),
              companyId: companyId,
              owner: HubSpotUtils.sanitizePropertyValue(deal.properties.hubspot_owner_id),
              customFields: customFields,
              // Store HubSpot values separately
              hubspot_name: dealName,
              hubspot_value: HubSpotUtils.formatCurrency(deal.properties.amount),
              hubspotId: hubspotId,
              // Preserve the original creation date from HubSpot
              createdAt: deal.properties.createdate || undefined
            };

            try {
              const createdDeal = this.dealRepository.createDeal(dealData, 'HubSpot');
              if (createdDeal) {
                importedCount++;
                created.push({ item: dealName });
              }
            } catch (error) {
              const errorMessage = error instanceof Error ? error.message : 'Unknown error';
              
              // If it's a UNIQUE constraint violation, the deal already exists
              if (errorMessage.includes('UNIQUE constraint')) {
                console.log(`Deal with HubSpot ID ${hubspotId} already exists, will update instead`);
                
                // Try to update the existing deal
                try {
                  const existingDeal = this.dealRepository.getDealByHubspotId(hubspotId);
                  if (existingDeal) {
                    const updateData = {
                      name: dealName,
                      stage: mappedStage,
                      value: HubSpotUtils.formatCurrency(deal.properties.amount),
                      probability: this.parseProbability(deal.properties.chance_of_win),
                      expectedCloseDate: deal.properties.closedate,
                      description: HubSpotUtils.sanitizePropertyValue(deal.properties.description),
                      owner: HubSpotUtils.sanitizePropertyValue(deal.properties.hubspot_owner_id),
                      customFields: customFields
                    };
                    
                    this.dealRepository.updateDeal(existingDeal.id, updateData, 'HubSpot');
                    importedCount++; // INCREMENT THE COUNT!
                    updates.push({ 
                      item: dealName, 
                      changes: ['Updated after duplicate detection'] 
                    });
                    
                    // Sync deal name to linked estimates if name changed
                    if (updateData.name && existingDeal.name !== updateData.name) {
                      const { updateEstimateFromDeal } = require('../../../utils/estimate-deal-sync');
                      const { DealEstimateRepository } = require('../../repositories/relationships/deal-estimate-repository');
                      const dealEstimateRepo = new DealEstimateRepository();
                      const linkedEstimates = dealEstimateRepo.getEstimatesForDeal(existingDeal.id);
                      
                      for (const est of linkedEstimates.filter(e => e.estimateType === 'internal')) {
                        updateEstimateFromDeal(existingDeal.id, est.estimateId).catch(err => {
                          console.error(`Error syncing deal name to estimate ${est.estimateId}:`, err);
                        });
                      }
                    }
                  }
                } catch (updateError) {
                  console.error(`Error updating existing deal "${dealName}":`, updateError);
                  errors.push({ 
                    item: dealName, 
                    error: `Failed to update existing deal: ${updateError}` 
                  });
                }
              } else {
                // For other errors, log and add to errors list
                console.error(`Error creating deal "${dealName}":`, errorMessage);
                errors.push({ 
                  item: dealName, 
                  error: errorMessage 
                });
              }
            }
          }
        } catch (dealError) {
          const errorMessage = dealError instanceof Error ? dealError.message : 'Unknown error';
          console.error(`Error processing deal "${dealName}":`, errorMessage);
          this.progressTracker.addError(dealName, errorMessage);
          errors.push({
            item: dealName,
            error: errorMessage
          });
        }
      }

      this.progressTracker.completeStep();

      // Log final summary for debugging
      console.log(`\nDeal Import Summary:`);
      console.log(`- Total deals in HubSpot: ${allDeals.length}`);
      console.log(`- Skipped (no company): ${errors.filter(e => e.error.includes('No company association')).length}`);
      console.log(`- Created: ${created.length}`);
      console.log(`- Updated: ${updates.length}`);
      console.log(`- Failed: ${errors.filter(e => !e.error.includes('No company association')).length}`);
      console.log(`- Total imported: ${importedCount}`);
      console.log(`- Should equal: ${created.length + updates.length} (created + updated)`);
      
      if (importedCount !== created.length + updates.length) {
        console.warn(`WARNING: Count mismatch! importedCount (${importedCount}) != created (${created.length}) + updated (${updates.length})`);
      }

      // Record the import
      HubSpotUtils.recordImport({
        type: 'deals',
        success: true,
        count: importedCount,
        errors: errors,
        createdCount: created.length,
        updatedCount: updates.length
      });

      // Log successful sync activity
      activityLogger.logHubSpotSyncCompleted({
        dealsCount: importedCount,
        companiesCount: 0,
        contactsCount: 0
      }).catch(error => {
        console.error('Error logging HubSpot sync completed activity:', error);
      });

      return this.progressTracker.createResult({
        success: true,
        count: importedCount,
        errors: errors,
        updates: updates,
        created: created
      });

    } catch (error) {
      console.error('Error importing deals from HubSpot:', error);

      let errorMessage = 'Unknown error';
      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (error && typeof error === 'object' && 'body' in error) {
        errorMessage = JSON.stringify((error as any).body);
      }

      HubSpotUtils.recordImport({
        type: 'deals',
        success: false,
        count: 0,
        errors: [{ item: 'Import failed', error: errorMessage }]
      });

      return this.progressTracker.createResult({
        success: false,
        count: 0,
        error: errorMessage
      });
    }
  }
}