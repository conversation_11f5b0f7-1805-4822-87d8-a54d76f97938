/* App.css - Main styling for the financial analysis application */

:root {
  --primary-color: #2870ab;
  --secondary-color: #3498db;
  --accent-color: #e74c3c;
  --text-color: #333;
  --background-color: #f9f9f9;
  --border-color: #ddd;
  --success-color: #27ae60;
  --warning-color: #f39c12;
  --chart-actual: #3498db;
  --chart-projected: #f39c12;
}

/* Dark mode CSS variables */
.dark {
  --primary-color: #5a9dd6;
  --secondary-color: #60a5fa;
  --accent-color: #f87171;
  --text-color: #e5e7eb;
  --background-color: #1f2937;
  --border-color: #374151;
  --success-color: #34d399;
  --warning-color: #fbbf24;
  --chart-actual: #60a5fa;
  --chart-projected: #fbbf24;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  color: var(--text-color);
  background-color: var(--background-color);
  line-height: 1.6;
}

.app {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* Header styles */
.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--border-color);
}

.app-header h1 {
  color: var(--primary-color);
  font-size: 24px;
}

.header-controls {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 10px;
}

.sync-controls {
  display: flex;
  align-items: center;
}

.last-synced {
  font-size: 14px;
  color: #777;
  margin-right: 15px;
}

button:not([class]),
.default-button {
  background-color: var(--secondary-color);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

button:not([class]):hover,
.default-button:hover {
  background-color: #2980b9;
}

button.syncing {
  background-color: #95a5a6;
  cursor: not-allowed;
}

/* Date range picker styles */
.date-control-container {
  margin-bottom: 30px;
}

.date-range-picker {
  background-color: white;
  border: 1px solid var(--border-color);
  border-radius: 5px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.date-inputs {
  display: flex;
  gap: 20px;
  margin-bottom: 15px;
}

.input-group {
  flex: 1;
}

.input-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  font-size: 14px;
}

.input-group input {
  width: 100%;
  padding: 8px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 14px;
}

.quick-ranges {
  display: flex;
  gap: 10px;
}

.quick-ranges button {
  flex: 1;
  background-color: #f0f0f0;
  color: var(--text-color);
  font-size: 13px;
}

.quick-ranges button:hover {
  background-color: #e0e0e0;
}

/* Summary container styles */
.summary-container {
  margin-bottom: 30px;
}

.cash-flow-summary {
  background-color: white;
  border: 1px solid var(--border-color);
  border-radius: 5px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.cash-flow-summary h2 {
  color: var(--primary-color);
  font-size: 18px;
  margin-bottom: 15px;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.summary-item {
  text-align: center;
  padding: 15px;
  border-radius: 5px;
  background-color: #f8f9fa;
}

.summary-item .label {
  font-size: 14px;
  color: #777;
  margin-bottom: 8px;
}

.summary-item .value {
  font-size: 24px;
  font-weight: 600;
}

.value.positive {
  color: var(--success-color);
}

.value.negative {
  color: var(--accent-color);
}

/* Chart container styles - transparent with no border */
.chart-container {
  margin-bottom: 30px;
  background-color: transparent;
  border: none;
  border-radius: 0;
  padding: 0;
  box-shadow: none;
}

.cash-flow-chart h2 {
  color: var(--primary-color);
  font-size: 18px;
  margin-bottom: 15px;
}

/* Data grid styles */
.data-grid-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 30px;
}

.work-in-progress,
.revenue-projections {
  background-color: white;
  border: 1px solid var(--border-color);
  border-radius: 5px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.work-in-progress h2,
.revenue-projections h2 {
  color: var(--primary-color);
  font-size: 18px;
  margin-bottom: 15px;
}

/* Legacy table styles - minimal defaults to avoid breaking existing tables */
table {
  border-collapse: collapse;
}

/* These styles are kept minimal to avoid overriding Tailwind utilities */
/* Tables that need specific styling should use Tailwind classes */

/* Metrics container styles */
.metrics-container {
  background-color: white;
  border: 1px solid var(--border-color);
  border-radius: 5px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.dark .metrics-container {
  background-color: #1f2937;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.metrics-container h2 {
  color: var(--primary-color);
  font-size: 18px;
  margin-bottom: 15px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.metric-item {
  text-align: center;
  padding: 15px;
  border-radius: 5px;
  background-color: #f8f9fa;
}

.metric-label {
  font-size: 14px;
  color: #777;
  margin-bottom: 8px;
}

.metric-value {
  font-size: 22px;
  font-weight: 600;
}

/* Loading and error states */
.app-loading,
.app-error {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.loading-indicator {
  background-color: white;
  padding: 20px 40px;
  border-radius: 5px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  text-align: center;
  font-size: 16px;
  color: var(--primary-color);
}

.error-message {
  background-color: #fff5f5;
  border: 1px solid #ffe0e0;
  padding: 20px 40px;
  border-radius: 5px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  text-align: center;
  max-width: 500px;
}

.error-message h2 {
  color: var(--accent-color);
  margin-bottom: 10px;
}

.error-message button {
  margin-top: 15px;
  background-color: var(--accent-color);
}

.error-message button:hover {
  background-color: #c0392b;
}

/* Budget Variance Styles - Merged from components/App.css */
.budget-variance-container {
  margin-top: 30px;
  padding: 20px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.budget-variance-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 15px;
}

.budget-variance-table th,
.budget-variance-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.budget-variance-table th {
  background-color: #f5f5f5;
  font-weight: 500;
  color: #424242;
}

.dark .budget-variance-table th {
  background-color: #374151;
  color: #e5e7eb;
}

.budget-variance-table tr:hover {
  background-color: #f9f9f9;
}

.dark .budget-variance-table tr:hover {
  background-color: #4b5563;
}

/* Legacy tables that don't use Tailwind - apply basic styles */
.legacy-table th,
.work-in-progress table th,
.revenue-projections table th {
  text-align: left;
  padding: 10px;
  border-bottom: 2px solid var(--border-color);
  font-weight: 600;
  font-size: 14px;
}

.legacy-table td,
.work-in-progress table td,
.revenue-projections table td {
  padding: 10px;
  border-bottom: 1px solid var(--border-color);
  font-size: 14px;
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.status-badge.on-track {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.status-badge.over {
  background-color: #ffebee;
  color: #c62828;
}

.status-badge.under {
  background-color: #e3f2fd;
  color: #2870ab;
}

.root-causes {
  margin: 0;
  padding-left: 20px;
  font-size: 13px;
}

.root-causes li {
  margin-bottom: 4px;
}

.status-on-track {
  border-left: 4px solid #4caf50;
}

.status-over {
  border-left: 4px solid #f44336;
}

.status-under {
  border-left: 4px solid #2870ab;
}

/* Section Styles - Merged from components/App.css */
section {
  margin-bottom: 30px;
  padding: 20px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

section h2 {
  margin-top: 0;
  color: #424242;
  font-size: 20px;
  font-weight: 500;
  margin-bottom: 20px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .summary-grid,
  .metrics-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .data-grid-container {
    grid-template-columns: 1fr;
  }

  .date-inputs {
    flex-direction: column;
    gap: 15px;
  }

  .app-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .sync-controls {
    margin-top: 15px;
  }

  .app {
    padding: 10px;
  }

  .actual-projected-grid {
    grid-template-columns: 1fr;
  }

  .budget-variance-table {
    font-size: 14px;
  }

  .budget-variance-table th,
  .budget-variance-table td {
    padding: 8px;
  }

  .root-causes {
    font-size: 12px;
  }
}
