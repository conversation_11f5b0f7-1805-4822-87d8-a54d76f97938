import React, { useState, useEffect } from "react";

/**
 * Individual summary card component for the cashflow projection
 * Less compact design following user request
 * Using inline styles to ensure consistency across devices
 */
const CashflowSummaryCard = ({
  title,
  value,
  subtitle,
  icon,
  cardClass,
  valueClass,
  className = "",
}) => {
  // State to track if we're on a small screen
  const [isSmallScreen, setIsSmallScreen] = useState(false);

  // Effect to check screen size on mount and resize
  useEffect(() => {
    const checkScreenSize = () => {
      setIsSmallScreen(window.innerWidth <= 480);
    };

    // Initial check
    checkScreenSize();

    // Add resize listener
    window.addEventListener("resize", checkScreenSize);

    // Cleanup
    return () => window.removeEventListener("resize", checkScreenSize);
  }, []);
  return (
    <div
      className={`cashflow-summary-card ${cardClass} ${className}`}
      style={{
        minHeight:
          "75px" /* Slightly increased to accommodate line height changes */,
        height: "100%",
        backgroundColor: "inherit",
        borderRadius: "0.5rem",
        boxShadow: "0 1px 2px rgba(0, 0, 0, 0.05)",
        overflow: "hidden",
        transition: "all 0.2s ease",
      }}
    >
      {/* Direct inline styles to ensure consistency across browsers and devices */}
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          justifyContent: "space-between",
          height: "100%",
          width: "100%",
          padding:
            "8px 10px" /* Reduced padding for better space utilization */,
        }}
        className="cashflow-card-content"
      >
        {/* Top section: Title and icon (icon moved to top right) */}
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "flex-start",
            width: "100%",
          }}
          className="card-top-section"
        >
          {/* Title and subtitle on the left */}
          <div>
            <h3
              style={{
                fontSize: "12px" /* Reduced font size for better fit */,
                fontWeight: 600,
                color: "inherit",
                overflow: "hidden",
                textOverflow: "ellipsis",
                whiteSpace: "nowrap",
                marginBottom: "1px" /* Reduced margin */,
                lineHeight: 1.1 /* Tighter line height */,
              }}
            >
              {title}
            </h3>
            {subtitle && (
              <p
                style={{
                  fontSize: "10px" /* Smaller font size */,
                  color: "inherit",
                  opacity: 0.7,
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  whiteSpace: "nowrap",
                  lineHeight: 1.3 /* Increased line height for descenders */,
                  margin: 0,
                  paddingBottom:
                    "2px" /* Added padding to prevent cutting off descenders */,
                }}
              >
                {subtitle}
              </p>
            )}
          </div>

          {/* Icon on the right */}
          {icon && (
            <div style={{ flexShrink: 0 }} className="card-icon">
              {icon}
            </div>
          )}
        </div>

        {/* Bottom section: Value */}
        <div
          style={{
            marginTop: "1px",
            textAlign: "left" /* Changed from right to left alignment */,
          }}
        >
          <p
            className={`cashflow-value ${
              isSmallScreen ? "mobile-value" : ""
            } ${valueClass}`}
            style={{
              fontSize: isSmallScreen
                ? "10px"
                : "16px" /* Dynamic size based on screen width */,
              fontWeight: isSmallScreen ? 600 : 700,
              color: "inherit",
              overflow: isSmallScreen ? "visible" : "hidden",
              textOverflow: isSmallScreen ? "clip" : "ellipsis",
              whiteSpace: "nowrap",
              lineHeight: 1.3 /* Increased line height for better readability */,
              margin: 0,
              paddingBottom:
                "1px" /* Added padding to prevent cutting off descenders */,
              opacity: 1,
              letterSpacing: isSmallScreen ? "-0.2px" : "normal",
            }}
          >
            {value}
          </p>
        </div>
      </div>
    </div>
  );
};

export default CashflowSummaryCard;
