# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage
Console logs.txt
/playwright-report/data
/test-results
/tests/e2e/screenshots

# production
/build
/dist
/data/dist

# misc
.DS_Store
.env
.env.local
.env.development
.env.test
.env.production
.serena
import log.txt

npm-debug.log*
yarn-debug.log*
yarn-error.log*
repomix-output.xml
docs/xero-node

# data files
/data/*.db
/data/*.db-shm
/data/*.db-wal
/data/*.json
/data/.write-test
\!/data/client-configs.json
database.sqlite
*.db-shm
*.db-wal

# temporary deployment directory
/deploy
/data/custom_expenses.json.bak

# Script and backup files
apply-test-improvements.sh
/backups

# Other Services - documentation only, not part of the application
/Other Services
.qodo
repomix-output.xml

# Test authentication and artifacts
tests/playwright/config/auth.json
tests/setup/auth.json
tests/visual/*.png
tests/e2e/artifacts/
debug-screenshot.png
debug-test.js
generate-auth.js
playwright-report/
tests/playwright/simpletest.spec.ts
dashboard-verification-error.png
auth.json
repomix-output.xml

**/CLAUDE.local.md
src/api/integrations/base.ts.bak
src/frontend/components/ForwardProjection/TransactionsList.tsx.bak2
src/services/cashflow-service.ts.bak
src/services/harvest/invoice-service.ts.bak
.aider*
debug.log
upstream.yml
node_modules
render_logs.md
browser_console_output.md
render logs.txt
browser_console_logs.txt
browser_console.txt
render_logs.txt
render_deployment_logs.txt
deployment_logs.txt
Production_old_working_logs.txt
NEW_logs.txt
Renderlogs.txt
docs/xero-node
node_modules
xero payrun AU API.txt
xero response.txt
docs/Xero API Examples/BalanceSheet.txt
repomix-output.txt
.repomix/bundles.json
node_modules
