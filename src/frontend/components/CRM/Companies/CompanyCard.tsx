import React from 'react';
import { Company, CompanyRelationshipType } from '../../../types/crm-types';

interface CompanyCardProps {
  company: Company;
  onClick: () => void;
}

/**
 * Card component for displaying a company
 */
const CompanyCard: React.FC<CompanyCardProps> = ({ company, onClick }) => {
  // Get company logo placeholder based on company name
  const getCompanyInitial = (name: string): string => {
    return name.charAt(0).toUpperCase();
  };

  // Get random color based on company ID for logo background
  const getLogoColor = (id: string | null | undefined): string => {
    const colors = [
      'bg-blue-500',
      'bg-green-500',
      'bg-purple-500',
      'bg-yellow-500',
      'bg-pink-500',
      'bg-indigo-500',
      'bg-red-500',
      'bg-teal-500'
    ];
    
    // If no ID, return a default color
    if (!id) {
      return colors[0];
    }
    
    // Simple hash function to get consistent color for the same ID
    const hash = id.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
    return colors[hash % colors.length];
  };
  
  // Format relationship type for display
  const formatRelationshipType = (type: CompanyRelationshipType): string => {
    switch (type) {
      case 'parent':
        return 'Parent';
      case 'subsidiary':
        return 'Subsidiary';
      case 'partner':
        return 'Partner';
      case 'acquisition':
        return 'Acquisition';
      default:
        return type.charAt(0).toUpperCase() + type.slice(1);
    }
  };

  return (
    <div
      className="company-card bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 cursor-pointer hover:shadow-md transition-shadow"
      onClick={onClick}
    >
      <div className="flex items-start space-x-4">
        {/* Company logo placeholder */}
        <div className={`flex-shrink-0 w-12 h-12 rounded-md ${getLogoColor(company.id)} flex items-center justify-center text-white font-medium text-lg`}>
          {getCompanyInitial(company.name)}
        </div>
        
        {/* Company info */}
        <div className="flex-1 min-w-0">
          <h3 className="text-base font-medium text-gray-900 dark:text-white truncate">
            {company.name}
          </h3>
          
          {company.industry && (
            <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
              {company.industry}
            </p>
          )}
          
          {company.size && (
            <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
              Size: {company.size}
            </p>
          )}
          
          {company.website && (
            <p className="text-sm text-gray-500 dark:text-gray-400 truncate mt-2">
              {company.website}
            </p>
          )}
          
          {company.address && (
            <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
              {company.address}
            </p>
          )}
          
          {/* Display parent company relationships */}
          {company.parentCompanies && company.parentCompanies.length > 0 && (
            <div className="mt-2">
              <span className="text-xs font-medium text-gray-500 dark:text-gray-400">
                Part of: {company.parentCompanies[0].company.name}
              </span>
              {company.parentCompanies.length > 1 && (
                <span className="text-xs text-gray-500 dark:text-gray-400"> +{company.parentCompanies.length - 1} more</span>
              )}
            </div>
          )}
          
          {/* Display child company relationships */}
          {company.childCompanies && company.childCompanies.length > 0 && (
            <div className="mt-1">
              <span className="text-xs font-medium text-gray-500 dark:text-gray-400">
                {company.childCompanies.length} related {company.childCompanies.length === 1 ? 'company' : 'companies'}
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CompanyCard;
