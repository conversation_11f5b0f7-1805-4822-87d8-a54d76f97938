/* Import responsive utilities */
@import "./frontend/styles/utilities/responsive-helpers.css";

/* Import modern design system */
@import "./frontend/styles/modern-design-system.css";

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Fluid Typography and Spacing System */
:root {
  /* Base sizes that scale with viewport */
  --text-sm: clamp(0.8rem, 0.17vw + 0.76rem, 0.89rem);
  --text-base: clamp(1rem, 0.34vw + 0.91rem, 1.19rem);
  --text-lg: clamp(1.25rem, 0.61vw + 1.1rem, 1.58rem);
  --text-xl: clamp(1.56rem, 1vw + 1.31rem, 2.11rem);
  --text-2xl: clamp(1.95rem, 1.56vw + 1.56rem, 2.81rem); /* Added 2xl */
  --text-3xl: clamp(2.44rem, 2.38vw + 1.88rem, 3.75rem); /* Added 3xl */

  /* Spacing that scales with viewport */
  --space-xs: clamp(0.5rem, 0.1vw + 0.4rem, 0.6rem);
  --space-sm: clamp(0.75rem, 0.2vw + 0.6rem, 0.9rem);
  --space-md: clamp(1rem, 0.3vw + 0.8rem, 1.2rem);
  --space-lg: clamp(1.5rem, 0.5vw + 1.2rem, 1.8rem);
  --space-xl: clamp(2rem, 0.75vw + 1.5rem, 2.5rem); /* Added xl */
  --space-2xl: clamp(2.5rem, 1vw + 2rem, 3.2rem); /* Added 2xl */
}

/* Utility classes for fluid typography */
.text-fluid-sm {
  font-size: var(--text-sm);
}
.text-fluid-base {
  font-size: var(--text-base);
}
.text-fluid-lg {
  font-size: var(--text-lg);
}
.text-fluid-xl {
  font-size: var(--text-xl);
}
.text-fluid-2xl {
  font-size: var(--text-2xl);
}
.text-fluid-3xl {
  font-size: var(--text-3xl);
}

/* Utility classes for fluid spacing (padding) */
.p-fluid-xs {
  padding: var(--space-xs);
}
.p-fluid-sm {
  padding: var(--space-sm);
}
.p-fluid-md {
  padding: var(--space-md);
}
.p-fluid-lg {
  padding: var(--space-lg);
}
.p-fluid-xl {
  padding: var(--space-xl);
}
.p-fluid-2xl {
  padding: var(--space-2xl);
}

.px-fluid-xs {
  padding-left: var(--space-xs);
  padding-right: var(--space-xs);
}
.px-fluid-sm {
  padding-left: var(--space-sm);
  padding-right: var(--space-sm);
}
.px-fluid-md {
  padding-left: var(--space-md);
  padding-right: var(--space-md);
}
.px-fluid-lg {
  padding-left: var(--space-lg);
  padding-right: var(--space-lg);
}
.px-fluid-xl {
  padding-left: var(--space-xl);
  padding-right: var(--space-xl);
}
.px-fluid-2xl {
  padding-left: var(--space-2xl);
  padding-right: var(--space-2xl);
}

.py-fluid-xs {
  padding-top: var(--space-xs);
  padding-bottom: var(--space-xs);
}
.py-fluid-sm {
  padding-top: var(--space-sm);
  padding-bottom: var(--space-sm);
}
.py-fluid-md {
  padding-top: var(--space-md);
  padding-bottom: var(--space-md);
}
.py-fluid-lg {
  padding-top: var(--space-lg);
  padding-bottom: var(--space-lg);
}
.py-fluid-xl {
  padding-top: var(--space-xl);
  padding-bottom: var(--space-xl);
}
.py-fluid-2xl {
  padding-top: var(--space-2xl);
  padding-bottom: var(--space-2xl);
}

/* Utility classes for fluid spacing (margin) */
.m-fluid-xs {
  margin: var(--space-xs);
}
.m-fluid-sm {
  margin: var(--space-sm);
}
.m-fluid-md {
  margin: var(--space-md);
}
.m-fluid-lg {
  margin: var(--space-lg);
}
.m-fluid-xl {
  margin: var(--space-xl);
}
.m-fluid-2xl {
  margin: var(--space-2xl);
}

.mx-fluid-xs {
  margin-left: var(--space-xs);
  margin-right: var(--space-xs);
}
.mx-fluid-sm {
  margin-left: var(--space-sm);
  margin-right: var(--space-sm);
}
.mx-fluid-md {
  margin-left: var(--space-md);
  margin-right: var(--space-md);
}
.mx-fluid-lg {
  margin-left: var(--space-lg);
  margin-right: var(--space-lg);
}
.mx-fluid-xl {
  margin-left: var(--space-xl);
  margin-right: var(--space-xl);
}
.mx-fluid-2xl {
  margin-left: var(--space-2xl);
  margin-right: var(--space-2xl);
}

.my-fluid-xs {
  margin-top: var(--space-xs);
  margin-bottom: var(--space-xs);
}
.my-fluid-sm {
  margin-top: var(--space-sm);
  margin-bottom: var(--space-sm);
}
.my-fluid-md {
  margin-top: var(--space-md);
  margin-bottom: var(--space-md);
}
.my-fluid-lg {
  margin-top: var(--space-lg);
  margin-bottom: var(--space-lg);
}
.my-fluid-xl {
  margin-top: var(--space-xl);
  margin-bottom: var(--space-xl);
}
.my-fluid-2xl {
  margin-top: var(--space-2xl);
  margin-bottom: var(--space-2xl);
}

/* Utility classes for fluid spacing (gap) */
.gap-fluid-xs {
  gap: var(--space-xs);
}
.gap-fluid-sm {
  gap: var(--space-sm);
}
.gap-fluid-md {
  gap: var(--space-md);
}
.gap-fluid-lg {
  gap: var(--space-lg);
}
.gap-fluid-xl {
  gap: var(--space-xl);
}
.gap-fluid-2xl {
  gap: var(--space-2xl);
}

/* Utility classes for fluid spacing (space-y) */
.space-y-fluid-xs > :not([hidden]) ~ :not([hidden]) {
  margin-top: var(--space-xs);
}
.space-y-fluid-sm > :not([hidden]) ~ :not([hidden]) {
  margin-top: var(--space-sm);
}
.space-y-fluid-md > :not([hidden]) ~ :not([hidden]) {
  margin-top: var(--space-md);
}
.space-y-fluid-lg > :not([hidden]) ~ :not([hidden]) {
  margin-top: var(--space-lg);
}
.space-y-fluid-xl > :not([hidden]) ~ :not([hidden]) {
  margin-top: var(--space-xl);
}
.space-y-fluid-2xl > :not([hidden]) ~ :not([hidden]) {
  margin-top: var(--space-2xl);
}

/* Utility classes for fluid spacing (space-x) */
.space-x-fluid-xs > :not([hidden]) ~ :not([hidden]) {
  margin-left: var(--space-xs);
}
.space-x-fluid-sm > :not([hidden]) ~ :not([hidden]) {
  margin-left: var(--space-sm);
}
.space-x-fluid-md > :not([hidden]) ~ :not([hidden]) {
  margin-left: var(--space-md);
}
.space-x-fluid-lg > :not([hidden]) ~ :not([hidden]) {
  margin-left: var(--space-lg);
}
.space-x-fluid-xl > :not([hidden]) ~ :not([hidden]) {
  margin-left: var(--space-xl);
}
.space-x-fluid-2xl > :not([hidden]) ~ :not([hidden]) {
  margin-left: var(--space-2xl);
}

@layer components {
  .card {
    @apply bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 dark:shadow-md;
  }

  .card-info {
    @apply bg-blue-50 dark:bg-blue-900 bg-opacity-50 dark:bg-opacity-20 border border-blue-100 dark:border-blue-800;
  }

  .card-success {
    @apply bg-green-50 dark:bg-green-900 bg-opacity-50 dark:bg-opacity-20 border border-green-100 dark:border-green-800;
  }

  .card-danger {
    @apply bg-red-50 dark:bg-red-900 bg-opacity-50 dark:bg-opacity-20 border border-red-100 dark:border-red-800;
  }

  .card-warning {
    @apply bg-yellow-50 dark:bg-yellow-900 bg-opacity-50 dark:bg-opacity-20 border border-yellow-100 dark:border-yellow-800;
  }

  .btn {
    @apply px-4 py-2 rounded-md font-medium transition-all duration-200 shadow-sm hover:shadow focus:outline-none focus:ring-2 focus:ring-opacity-50;
  }

  .btn-primary {
    @apply bg-secondary text-white hover:bg-secondary-600 focus:ring-secondary focus:ring-opacity-50;
  }

  .btn-secondary {
    @apply bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-600 focus:ring-gray-300 dark:focus:ring-gray-600;
  }

  .btn-danger {
    @apply bg-accent text-white hover:bg-accent-600 focus:ring-accent focus:ring-opacity-50;
  }

  .btn-success {
    @apply bg-success text-white hover:bg-success-600 focus:ring-success focus:ring-opacity-50;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5;
  }

  .form-input {
    @apply block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-secondary focus:ring focus:ring-secondary focus:ring-opacity-20 sm:text-sm px-3 py-2 border transition-all duration-200 hover:border-gray-400 dark:hover:border-gray-500 dark:bg-gray-700 dark:text-gray-200;
  }

  .form-select {
    @apply block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-secondary focus:ring focus:ring-secondary focus:ring-opacity-20 sm:text-sm px-3 py-2 border transition-all duration-200 appearance-none bg-white dark:bg-gray-700 hover:border-gray-400 dark:hover:border-gray-500 dark:text-gray-200;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
  }

  .form-group {
    @apply mb-4;
  }

  /* Disabled state for form elements */
  .form-input:disabled,
  .form-select:disabled {
    @apply bg-gray-100 dark:bg-gray-800 border-gray-200 dark:border-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed;
  }

  .amount-positive {
    @apply text-success font-semibold;
  }

  .amount-negative {
    @apply text-accent font-semibold;
  }

  /* Enhanced table styling */
  .table-container {
    @apply overflow-x-auto rounded-lg border border-gray-200 dark:border-gray-700;
  }

  .table-header {
    @apply px-2 py-2 bg-gray-50 dark:bg-gray-800 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider;
  }

  .table-cell {
    @apply px-2 py-2 dark:text-gray-300;
  }

  .table-cell-nowrap {
    @apply whitespace-nowrap;
  }

  .table-row {
    @apply hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150;
  }

  /* Responsive Container using Container Queries */
  .responsive-container {
    container-type: inline-size; /* Establish containment context */
    display: flex;
    flex-direction: column; /* Default to column layout */
    gap: var(--space-md); /* Use fluid gap */
  }

  /* Apply row layout when container width meets the breakpoint */
  @container (min-width: var(--responsive-breakpoint, 600px)) {
    .responsive-container {
      flex-direction: row;
      align-items: flex-start; /* Align items at the start in row layout */
    }
  }

  /* Pragmatic Responsive Styles for DecisionTable */
  @media (max-width: 767px) {
    /* Below md breakpoint */
    /* Hide table header on small screens */
    .decision-table thead {
      @apply hidden;
    }
    /* Make table and rows block elements */
    .decision-table,
    .decision-table tbody,
    .decision-table tr,
    .decision-table td {
      @apply block;
    }
    /* Add spacing between stacked rows */
    .decision-table tr {
      @apply mb-4 border border-gray-200 dark:border-gray-700 rounded;
    }
    /* Style cells for vertical stacking */
    .decision-table td {
      @apply py-2 px-3 text-right border-b border-gray-100 dark:border-gray-700;
      /* Add label using ::before pseudo-element */
      position: relative;
      padding-left: 45%; /* Make space for label */
      text-align: right;
    }
    .decision-table td::before {
      content: attr(data-label); /* Use data-label attribute for label */
      position: absolute;
      left: 0;
      width: 40%; /* Adjust width as needed */
      padding-left: 0.75rem; /* px-3 */
      padding-right: 0.75rem; /* px-3 */
      font-weight: 600; /* font-semibold */
      text-align: left;
      white-space: nowrap;
      color: theme("colors.gray.500");
      @apply text-xs;
    }
    /* Remove bottom border for last cell in a stacked row */
    .decision-table td:last-child {
      @apply border-b-0;
    }
    /* Ensure expand/collapse cell spans full width */
    .decision-table td[colspan="6"] {
      padding-left: 0.75rem; /* px-3 */
      text-align: left;
    }
    .decision-table td[colspan="6"]::before {
      display: none; /* Hide label for full-span cell */
    }
  }
}
