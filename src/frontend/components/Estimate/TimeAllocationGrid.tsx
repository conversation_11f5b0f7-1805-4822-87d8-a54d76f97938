import React, { useState, useCallback, useRef, useEffect } from 'react';
import { formatCurrency } from './utils';
import { AllocationWithTotals } from '../../hooks/useEstimateStaffManagement';
import { WeekInfo } from '../../types/estimate-types';
import AllocationInput from './AllocationInput';

interface TimeAllocationGridProps {
  /**
   * Staff allocations with calculated totals
   */
  allocationsWithTotals: AllocationWithTotals[];

  /**
   * Week information for columns
   */
  weeks: WeekInfo[];

  /**
   * Handler for changing allocation values
   */
  onAllocationChange: (internalId: string, weekIdentifier: string, days: number) => void;

  /**
   * Whether the grid is in read-only mode
   */
  isReadOnly?: boolean;
}

/**
 * Displays a grid for allocating staff time across weeks
 */
const TimeAllocationGrid: React.FC<TimeAllocationGridProps> = ({
  allocationsWithTotals,
  weeks,
  onAllocationChange,
  isReadOnly = false,
}) => {
  // State for fullscreen mode
  const [isFullscreen, setIsFullscreen] = useState<boolean>(false);
  const timeAllocationRef = useRef<HTMLDivElement>(null);

  // Handle escape key to exit fullscreen
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isFullscreen) {
        setIsFullscreen(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    // Lock body scroll when in fullscreen
    if (isFullscreen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = '';
    };
  }, [isFullscreen]);

  // Calculate project totals (days only - we don't need the full financial totals here)
  const totalDays = allocationsWithTotals.reduce((sum, staff) => sum + staff.totalDays, 0);

  // Calculate weekly totals - use integer arithmetic to avoid floating point errors
  const weeklyTotals = weeks.map(week => {
    const totalTenths = allocationsWithTotals.reduce(
      (sum, staff) => {
        const days = staff.weeklyAllocation[week.identifier] || 0;
        return sum + Math.round(days * 10);
      },
      0
    );
    return Math.round(totalTenths) / 10;
  });

  return (
    <div
      ref={timeAllocationRef}
      className={`${
        isFullscreen
          ? 'fixed inset-0 z-50 p-4 bg-white dark:bg-gray-900 overflow-auto'
          : 'w-full bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm p-4'
      } transition-all duration-300 ease-in-out`}>
      <div className="flex justify-between items-center mb-3">
        <div className="flex items-center space-x-2">
          <h3 className="font-semibold text-gray-800 dark:text-gray-200">Time Allocation</h3>
          {/* Fullscreen toggle button */}
          <button
            onClick={() => setIsFullscreen(!isFullscreen)}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 focus:outline-none"
            title={isFullscreen ? "Exit full screen" : "View in full screen"}
          >
            {isFullscreen ? (
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            ) : (
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5v-4m0 4h-4m4 0l-5-5" />
              </svg>
            )}
          </button>
        </div>
        <div className="flex items-center">
          {isFullscreen && (
            <span className="text-xs text-gray-500 dark:text-gray-400">
              Press ESC to exit fullscreen
            </span>
          )}
        </div>
      </div>
      {/* Time Allocation Table with sticky columns */}
      <div className="overflow-hidden border border-gray-100 dark:border-gray-700 rounded-md">
        <div className="overflow-x-auto">
          <table className="w-full">
            <colgroup>
              <col className="w-[180px]" />
              {weeks.map((week) => (
                <col key={week.identifier} className="w-[60px]" />
              ))}
              <col className="w-[80px]" />
              <col className="w-[100px]" />
              <col className="w-[100px]" />
            </colgroup>
            <thead>
              <tr className="bg-gray-50 dark:bg-gray-700">
                {/* Team member header - sticky left */}
                <th className="sticky left-0 z-30 bg-gray-50 dark:bg-gray-700 px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider shadow-sm">
                  <div className="whitespace-nowrap">Team</div>
                  <div className="text-[0.65rem] font-normal">Member</div>
                </th>

                {/* Week headers */}
                {weeks.map((week) => {
                  const parts = week.shortLabel.split(' ');
                  const weekNum = parts[0];
                  const dateStr = parts.length > 1 ? parts[1] : '';

                  return (
                    <th
                      key={week.identifier}
                      className={`px-2 py-2 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider ${week.hasAlternatingBackground ? 'bg-gray-100 dark:bg-gray-700/80' : ''}`}
                    >
                      <div className="whitespace-nowrap">{weekNum}</div>
                      <div className="text-[0.65rem] font-normal">{dateStr}</div>
                    </th>
                  );
                })}

                {/* Total columns - sticky right */}
                <th className="sticky right-[200px] z-20 bg-gray-50 dark:bg-gray-700 px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider shadow-sm">
                  <div className="whitespace-nowrap">Total</div>
                  <div className="text-[0.65rem] font-normal">Days</div>
                </th>
                <th className="sticky right-[100px] z-20 bg-gray-50 dark:bg-gray-700 px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider shadow-sm">
                  <div className="whitespace-nowrap">Total</div>
                  <div className="text-[0.65rem] font-normal">Cost</div>
                </th>
                <th className="sticky right-0 z-20 bg-gray-50 dark:bg-gray-700 px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider shadow-sm">
                  <div className="whitespace-nowrap">Total</div>
                  <div className="text-[0.65rem] font-normal">Fees</div>
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {allocationsWithTotals.map((staff) => (
                <tr key={staff.internalId} className={`hover:bg-gray-50 dark:hover:bg-gray-700/50 ${staff.isPlaceholder ? 'bg-gray-50/50 dark:bg-gray-700/20' : ''}`}>
                  {/* Team member - sticky left */}
                  <td className="sticky left-0 z-30 bg-white dark:bg-gray-800 px-3 py-2 text-sm text-gray-700 dark:text-gray-300 whitespace-nowrap shadow-sm">
                    {staff.firstName || ""} {staff.lastName || ''}{staff.isPlaceholder && <span className="text-xs text-gray-500 italic ml-1">(placeholder)</span>}
                  </td>

                  {/* Week data cells */}
                  {weeks.map((week) => {
                    const allocation = staff.weeklyAllocation[week.identifier] || 0;

                    return (
                      <td key={`${staff.internalId}-${week.identifier}`} className={`px-2 py-2 text-center z-0 ${week.hasAlternatingBackground ? 'bg-gray-50 dark:bg-gray-700/40' : ''}`}>
                        {isReadOnly ? (
                          <span className="inline-block text-sm text-gray-800 dark:text-gray-300 text-center">
                            {allocation > 0 ? allocation.toFixed(1) : '-'}
                          </span>
                        ) : (
                          <AllocationInput
                            value={allocation}
                            staffId={staff.internalId}
                            weekId={week.identifier}
                            onAllocationChange={onAllocationChange}
                          />
                        )}
                      </td>
                    );
                  })}

                  {/* Total columns - sticky right */}
                  <td className="sticky right-[200px] z-10 bg-white dark:bg-gray-800 px-3 py-2 text-right text-sm text-gray-700 dark:text-gray-300 font-medium whitespace-nowrap shadow-sm">
                    {staff.totalDays.toFixed(1)}
                  </td>
                  <td className="sticky right-[100px] z-10 bg-white dark:bg-gray-800 px-3 py-2 text-right text-sm text-red-600 dark:text-red-400 whitespace-nowrap shadow-sm">
                    {formatCurrency(staff.totalCost)}
                  </td>
                  <td className="sticky right-0 z-10 bg-white dark:bg-gray-800 px-3 py-2 text-right text-sm text-green-600 dark:text-green-400 whitespace-nowrap shadow-sm">
                    {formatCurrency(staff.totalFees)}
                  </td>
                </tr>
              ))}
            </tbody>
            <tfoot className="bg-gray-50 dark:bg-gray-700">
              <tr>
                {/* Weekly totals header - sticky left */}
                <td className="sticky left-0 z-30 bg-gray-50 dark:bg-gray-700 px-3 py-2 text-sm text-gray-800 dark:text-gray-200 font-semibold whitespace-nowrap shadow-sm">
                  Weekly Totals
                </td>

                {/* Weekly totals */}
                {weeks.map((week, index) => (
                  <td key={`total-${week.identifier}`} className={`px-2 py-2 text-center text-sm text-gray-800 dark:text-gray-200 font-semibold z-0 ${week.isWeekend ? 'bg-gray-100 dark:bg-gray-600/50' : ''}`}>
                    {weeklyTotals[index].toFixed(1)}
                  </td>
                ))}

                {/* Total columns - sticky right */}
                <td className="sticky right-[200px] z-20 bg-gray-50 dark:bg-gray-700 px-3 py-2 text-right text-sm text-gray-800 dark:text-gray-200 font-semibold whitespace-nowrap shadow-sm">
                  {totalDays.toFixed(1)}
                </td>
                <td className="sticky right-[100px] z-20 bg-gray-50 dark:bg-gray-700 px-3 py-2 text-right text-sm text-gray-800 dark:text-gray-200 font-semibold whitespace-nowrap shadow-sm">
                  {formatCurrency(allocationsWithTotals.reduce((sum, staff) => sum + staff.totalCost, 0))}
                </td>
                <td className="sticky right-0 z-20 bg-gray-50 dark:bg-gray-700 px-3 py-2 text-right text-sm text-gray-800 dark:text-gray-200 font-semibold whitespace-nowrap shadow-sm">
                  {formatCurrency(allocationsWithTotals.reduce((sum, staff) => sum + staff.totalFees, 0))}
                </td>
              </tr>
            </tfoot>
          </table>
        </div>
      </div>
    </div>
  );
};

export default TimeAllocationGrid;