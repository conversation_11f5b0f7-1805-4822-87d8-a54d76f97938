-- Remove duplicate deal_contact table
-- The contact_role table is the actively used table for deal-contact relationships

-- Enable foreign keys
PRAGMA foreign_keys = ON;

-- Drop indexes first
DROP INDEX IF EXISTS idx_deal_contact_deal;
DROP INDEX IF EXISTS idx_deal_contact_contact;

-- Drop the duplicate table
DROP TABLE IF EXISTS deal_contact;

-- Add migration tracking
INSERT INTO migrations (id, name, batch) 
VALUES ('006', '006_remove_duplicate_deal_contact_table', 6);