import React, { useMemo } from "react";
import { useDrop } from "react-dnd";
import { Deal, DealStage } from "../../../types/crm-types";
import DealCard from "./DealCard";

// Define the drag item type
export const DEAL_DRAG_TYPE = "deal";

interface DealColumnProps {
  stage: DealStage;
  deals: Deal[];
  onMoveDeal: (dealId: string, newStage: DealStage) => void;
  onSelectDeal: (deal: Deal) => void;
  isCompactView?: boolean;
  isCollapsed?: boolean;
  onToggleCollapse: () => void;
  isInactive?: boolean;
  hideInactive?: boolean;
}

/**
 * Column component for a specific deal stage
 */
const DealColumn: React.FC<DealColumnProps> = ({
  stage,
  deals,
  onMoveDeal,
  onSelectDeal,
  isCompactView = false,
  isCollapsed = false,
  onToggleCollapse,
  isInactive = false,
  hideInactive = false,
}) => {
  // Set up drop target for this column
  const [{ isOver }, drop] = useDrop({
    accept: DEAL_DRAG_TYPE,
    drop: (item: { id: string; stage: DealStage }) => {
      // Only move if the stage is different
      if (item.stage !== stage) {
        onMoveDeal(item.id, stage);
      }
    },
    collect: (monitor) => ({
      isOver: !!monitor.isOver(),
    }),
  });

  // Get stage color based on stage name
  const getStageColor = (stage: DealStage): string => {
    switch (stage) {
      case "Identified":
        return "bg-blue-100 dark:bg-blue-900";
      case "Qualified":
        return "bg-indigo-100 dark:bg-indigo-900";
      case "Solution proposal":
        return "bg-purple-100 dark:bg-purple-900";
      case "Solution presentation":
        return "bg-violet-100 dark:bg-violet-900";
      case "Objection handling":
        return "bg-amber-100 dark:bg-amber-900";
      case "Finalising terms":
        return "bg-orange-100 dark:bg-orange-900";
      case "Closed won":
        return "bg-green-100 dark:bg-green-900";
      case "Closed lost":
        return "bg-red-100 dark:bg-red-900";
      case "Abandoned":
        return "bg-gray-100 dark:bg-gray-800";
      default:
        return "bg-gray-100 dark:bg-gray-800";
    }
  };

  // Get stage text color based on stage name
  const getStageTextColor = (stage: DealStage): string => {
    switch (stage) {
      case "Identified":
        return "text-blue-800 dark:text-blue-200";
      case "Qualified":
        return "text-indigo-800 dark:text-indigo-200";
      case "Solution proposal":
        return "text-purple-800 dark:text-purple-200";
      case "Solution presentation":
        return "text-violet-800 dark:text-violet-200";
      case "Objection handling":
        return "text-amber-800 dark:text-amber-200";
      case "Finalising terms":
        return "text-orange-800 dark:text-orange-200";
      case "Closed won":
        return "text-green-800 dark:text-green-200";
      case "Closed lost":
        return "text-red-800 dark:text-red-200";
      case "Abandoned":
        return "text-gray-800 dark:text-gray-200";
      default:
        return "text-gray-800 dark:text-gray-200";
    }
  };

  // Format currency value
  const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat("en-AU", {
      style: "currency",
      currency: "AUD",
      maximumFractionDigits: 0,
    }).format(value);
  };

  // Format currency in a more compact way for compact view
  const formatCompactCurrency = (value: number): string => {
    // For values less than 1000, just show the number with $ sign
    if (value < 1000) {
      return "$" + value;
    }

    // For values 1000-999999, show as $Xk (e.g., $10k, $100k)
    if (value < 1000000) {
      return "$" + Math.round(value / 1000) + "k";
    }

    // For values 1000000+, show as $XM (e.g., $1M, $10M)
    return "$" + (value / 1000000).toFixed(1) + "M";
  };

  // Calculate total value of deals in this column
  const totalValue = useMemo(() => {
    return deals.reduce((sum, deal) => sum + (deal.value || 0), 0);
  }, [deals]);

  // Determine column width based on state
  const getColumnWidthClass = () => {
    if (isCollapsed) {
      return "min-w-[40px]";
    }
    if (isInactive && hideInactive && !isCollapsed) {
      // This is a special case - an inactive column that was expanded while hideInactive is true
      return "inactive-expanded";
    }
    if (isCompactView) {
      return "deal-column--compact";
    }
    return "";
  };

  return (
    <div
      ref={drop}
      className={`deal-column ${getColumnWidthClass()} ${
        isOver ? "bg-gray-50 dark:bg-gray-700" : ""
      } ${
        isCompactView
          ? "border-r border-gray-200 dark:border-gray-700 flex-1"
          : ""
      } transition-all duration-300 ease-in-out`}
    >
      {/* Column header */}
      <div
        className={`deal-column__header ${
          isCompactView ? "deal-column__header--compact" : ""
        } ${getStageColor(stage)} flex justify-between items-center`}
      >
        {isCollapsed ? (
          <div className="w-full flex justify-center">
            <button
              onClick={onToggleCollapse}
              className="text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 focus:outline-none"
              title="Expand column"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </button>
          </div>
        ) : (
          <div className="flex flex-col flex-grow">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <h3
                  className={`font-medium ${
                    isCompactView ? "text-2xs" : "text-sm"
                  } ${getStageTextColor(stage)} truncate max-w-[90%]`}
                >
                  {stage}
                </h3>
                <span
                  className={`${
                    isCompactView ? "ml-0.5 text-2xs" : "ml-1 text-xs"
                  } font-normal text-gray-600 dark:text-gray-400`}
                >
                  ({deals.length})
                </span>
              </div>
              <button
                onClick={onToggleCollapse}
                className="text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 focus:outline-none ml-1"
                title="Collapse column"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 19l-7-7 7-7"
                  />
                </svg>
              </button>
            </div>
            <div
              className={`${
                isCompactView ? "text-2xs" : "text-xs"
              } font-medium text-gray-600 dark:text-gray-400 ${
                isCompactView ? "mt-0" : "mt-0.5"
              }`}
            >
              {isCompactView
                ? formatCompactCurrency(totalValue)
                : formatCurrency(totalValue)}
            </div>
          </div>
        )}
      </div>

      {/* Column content - list of deal cards */}
      {!isCollapsed && (
        <div
          className={`deal-column__content ${
            isCompactView ? "px-1 py-0.5" : "p-1"
          } flex-1 overflow-y-auto`}
        >
          {deals.length > 0 ? (
            <div className={`${isCompactView ? "space-y-1" : "space-y-1.5"}`}>
              {deals.map((deal) => (
                <DealCard
                  key={deal.id}
                  deal={deal}
                  onSelect={() => onSelectDeal(deal)}
                  isCompactView={isCompactView}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-2 text-gray-400 dark:text-gray-600">
              <p className={`${isCompactView ? "text-2xs" : "text-xs"}`}>
                {isCompactView ? "Empty" : "No deals"}
              </p>
              {!isCompactView && <p className="text-xs">Drag deals here</p>}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default DealColumn;
