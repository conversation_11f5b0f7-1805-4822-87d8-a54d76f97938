import React, { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "react-query";
import {
  checkHubSpotStatus,
  importAllFromHubSpot,
  getHubSpotImportHistory,
} from "../../../api/hubspot";
import { HubSpotImport as HubSpotImportType } from "../../../types/crm-types";
import ImportProgressTracker from "./ImportProgressTracker";
import ImportSummary from "./ImportSummary";

/**
 * Component for importing data from HubSpot
 */
const HubSpotImport: React.FC = () => {
  const [activeTab, setActiveTab] = useState<"import" | "history">("import");
  const [showProgress, setShowProgress] = useState(false);
  const [importResults, setImportResults] = useState<any>(null);
  const queryClient = useQueryClient();

  // Fetch HubSpot status
  const { data: hubspotStatus, isLoading: isStatusLoading } = useQuery(
    "hubspotStatus",
    checkHubSpotStatus
  );

  // Fetch import history
  const { data: importHistory = [], isLoading: isHistoryLoading } = useQuery(
    "hubspotImportHistory",
    getHubSpotImportHistory,
    {
      enabled: activeTab === "history",
    }
  );

  // Mutation for importing all data with retry disabled
  const importAllMutation = useMutation(() => importAllFromHubSpot(), {
    retry: false, // Disable automatic retries
    onMutate: () => {
      setShowProgress(true);
      setImportResults(null);
    },
    onSuccess: (data) => {
      setImportResults(data);
      setShowProgress(false);
      // Clear existing data and let it refetch naturally
      queryClient.removeQueries("deals");
      queryClient.removeQueries("contacts");
      queryClient.removeQueries("companies");
      queryClient.invalidateQueries("hubspotImportHistory");
    },
    onError: () => {
      setShowProgress(false);
    },
  });

  // Handle importing all data with double-click protection
  const handleImportAll = () => {
    // Prevent double-clicks
    if (importAllMutation.isLoading) {
      console.log('Import already in progress, ignoring click');
      return;
    }
    importAllMutation.mutate();
  };


  // Format date for display
  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleString("en-AU", {
      day: "numeric",
      month: "short",
      year: "numeric",
      hour: "numeric",
      minute: "2-digit",
    });
  };

  // Get status badge color
  const getStatusBadgeColor = (status: HubSpotImportType["status"]): string => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      case "completed_with_warnings":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";
      case "failed":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
      case "pending":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200";
    }
  };
  
  // Get status display text
  const getStatusDisplayText = (status: HubSpotImportType["status"]): string => {
    switch (status) {
      case "completed":
        return "Completed";
      case "completed_with_warnings":
        return "Completed with exceptions";
      case "failed":
        return "Failed";
      case "pending":
        return "Pending";
      default:
        return status;
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
      <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
        HubSpot Data Import
      </h2>

      {/* Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700 mb-4">
        <nav className="-mb-px flex space-x-8">
          <button
            className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === "import"
                ? "border-blue-500 text-blue-600 dark:text-blue-400"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600"
            }`}
            onClick={() => setActiveTab("import")}
          >
            Import Data
          </button>
          <button
            className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === "history"
                ? "border-blue-500 text-blue-600 dark:text-blue-400"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600"
            }`}
            onClick={() => setActiveTab("history")}
          >
            Import History
          </button>
        </nav>
      </div>

      {isStatusLoading ? (
        <div className="flex justify-center items-center h-24">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : !hubspotStatus?.isConfigured ? (
        <div className="bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-800 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-yellow-400"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                HubSpot integration not configured
              </h3>
              <p className="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                Please configure the HubSpot integration in the settings tab
                before importing data.
              </p>
            </div>
          </div>
        </div>
      ) : activeTab === "import" ? (
        <div className="space-y-6">
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Import data from your HubSpot account. This will fetch the latest
            data and merge it with your existing CRM data.
          </p>

                  >
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-green-800 dark:text-green-200">
                    CRM data reset successful
                  </h3>
                  <p className="mt-2 text-sm text-green-700 dark:text-green-300">
                    Deleted {resetCRMDataMutation.data?.dealsDeleted} deals,{" "}
                    {resetCRMDataMutation.data?.contactsDeleted} contacts,{" "}
                    {resetCRMDataMutation.data?.companiesDeleted} companies, and{" "}
                    {resetCRMDataMutation.data?.notesDeleted} notes.
                  </p>
                </div>
              </div>
            </div>
          )}

          <div className="max-w-2xl mx-auto">
            {/* Show ImportProgressTracker when importing */}
            {showProgress && (
              <div className="mb-6">
                <ImportProgressTracker
                  isImporting={showProgress}
                  onComplete={(results) => {
                    setImportResults(results);
                    setShowProgress(false);
                  }}
                />
              </div>
            )}

            {/* Show ImportSummary when results are available */}
            {importResults && !showProgress && (
              <div className="mb-6">
                <ImportSummary
                  results={importResults.results}
                  totalCount={importResults.totalCount}
                  onClose={() => setImportResults(null)}
                />
              </div>
            )}

            {/* Only show import button when not importing and no results */}
            {!showProgress && !importResults && (
              <>
                {/* Import All Data */}
                <div className="bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-6 shadow-sm">
                  <h3 className="text-xl font-medium text-gray-900 dark:text-white mb-3">
                    Import All HubSpot Data
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mb-6">
                    Import all data from HubSpot in the correct order: companies
                    first, then deals (with proper company associations), then
                    contacts. Existing records will be updated automatically.
                  </p>
                  <div className="space-y-3 mb-4">
                    <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md p-3">
                      <p className="text-xs text-blue-700 dark:text-blue-300">
                        <strong>Note:</strong> Deals without company associations will be skipped. 
                        Make sure all deals are linked to companies in HubSpot before importing.
                      </p>
                    </div>
                    <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-md p-3">
                      <p className="text-xs text-amber-700 dark:text-amber-300">
                        <strong>Important:</strong> Large imports may take 2-5 minutes to complete. 
                        Please wait for the import to finish - do not refresh the page.
                      </p>
                    </div>
                  </div>
                  <button
                    type="button"
                    onClick={handleImportAll}
                    className="w-full px-6 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    disabled={importAllMutation.isLoading}
                  >
                    Import All Data
                  </button>
                </div>
              </>
            )}

            {/* Button to start new import when results are showing */}
            {importResults && !showProgress && (
              <div className="mt-4 text-center">
                <button
                  type="button"
                  onClick={() => setImportResults(null)}
                  className="text-sm text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
                >
                  Start New Import
                </button>
              </div>
            )}
          </div>
        </div>
      ) : (
        <div>
          {isHistoryLoading ? (
            <div className="flex justify-center items-center h-24">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
            </div>
          ) : importHistory.length === 0 ? (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              <p>No import history found.</p>
              <p className="text-sm">
                Import data from HubSpot to see the history here.
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                    >
                      Date
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                    >
                      Status
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                    >
                      Deals
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                    >
                      Contacts
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                    >
                      Companies
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                    >
                      Notes
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                    >
                      Associations
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {importHistory.map((importItem) => (
                    <tr key={importItem.id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {formatDate(importItem.importDate)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeColor(
                            importItem.status
                          )}`}
                        >
                          {getStatusDisplayText(importItem.status)}
                        </span>
                        {importItem.errorMessage && (
                          <span className={`block mt-1 text-xs ${
                            importItem.status === 'completed_with_warnings' 
                              ? 'text-yellow-600 dark:text-yellow-400' 
                              : 'text-red-600 dark:text-red-400'
                          }`}>
                            {importItem.errorMessage}
                          </span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {importItem.dealsCount || "-"}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {importItem.contactsCount || "-"}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {importItem.companiesCount || "-"}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {importItem.notesCount || "-"}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {importItem.associationsCount || "-"}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default HubSpotImport;
