/**
 * Repository for managing estimate allocations
 */

import { v4 as uuidv4 } from 'uuid';
import { BaseRepository } from './base-repository';

/**
 * Interface for estimate allocation entity
 */
export interface EstimateAllocation {
  id: string;
  estimateId: string;
  harvestUserId: number;
  firstName: string;
  lastName?: string;
  projectRole?: string;
  level?: string;
  targetRateDaily: number;
  costRateDaily: number;
  proposedRateDaily: number;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
}

/**
 * Interface for creating a new estimate allocation
 */
export interface EstimateAllocationCreate {
  estimateId: string;
  harvestUserId: number;
  firstName: string;
  lastName?: string;
  projectRole?: string;
  level?: string;
  targetRateDaily: number;
  costRateDaily: number;
  proposedRateDaily: number;
}

/**
 * Interface for updating an estimate allocation
 */
export interface EstimateAllocationUpdate {
  firstName?: string;
  lastName?: string;
  projectRole?: string;
  level?: string;
  targetRateDaily?: number;
  costRateDaily?: number;
  proposedRateDaily?: number;
}

/**
 * Repository for managing estimate allocations
 */
export class EstimateAllocationRepository extends BaseRepository {
  /**
   * Constructor
   */
  constructor() {
    super('estimate_allocation');
  }

  /**
   * Get all allocations for an estimate
   * @param estimateId Estimate ID
   * @returns Array of allocations
   */
  getAllocationsByEstimate(estimateId: string): EstimateAllocation[] {
    try {
      const allocations = this.db.prepare(`
        SELECT 
          id,
          estimate_id as estimateId,
          harvest_user_id as harvestUserId,
          first_name as firstName,
          last_name as lastName,
          project_role as projectRole,
          level,
          target_rate_daily as targetRateDaily,
          cost_rate_daily as costRateDaily,
          proposed_rate_daily as proposedRateDaily,
          created_at as createdAt,
          updated_at as updatedAt
        FROM ${this.tableName}
        WHERE estimate_id = ?
        ORDER BY first_name, last_name
      `).all(estimateId) as EstimateAllocation[];

      return allocations;
    } catch (error) {
      console.error(`Error fetching allocations for estimate ${estimateId}:`, error);
      return [];
    }
  }

  /**
   * Get allocation by ID
   * @param id Allocation ID
   * @returns Allocation or null if not found
   */
  getAllocationById(id: string): EstimateAllocation | null {
    try {
      const allocation = this.db.prepare(`
        SELECT 
          id,
          estimate_id as estimateId,
          harvest_user_id as harvestUserId,
          first_name as firstName,
          last_name as lastName,
          project_role as projectRole,
          level,
          target_rate_daily as targetRateDaily,
          cost_rate_daily as costRateDaily,
          proposed_rate_daily as proposedRateDaily,
          created_at as createdAt,
          updated_at as updatedAt
        FROM ${this.tableName}
        WHERE id = ?
      `).get(id) as EstimateAllocation | undefined;

      return allocation || null;
    } catch (error) {
      console.error(`Error fetching allocation by ID ${id}:`, error);
      return null;
    }
  }

  /**
   * Get allocation by estimate and Harvest user ID
   * @param estimateId Estimate ID
   * @param harvestUserId Harvest user ID
   * @returns Allocation or null if not found
   */
  getAllocationByEstimateAndUser(estimateId: string, harvestUserId: number): EstimateAllocation | null {
    try {
      const allocation = this.db.prepare(`
        SELECT 
          id,
          estimate_id as estimateId,
          harvest_user_id as harvestUserId,
          first_name as firstName,
          last_name as lastName,
          project_role as projectRole,
          level,
          target_rate_daily as targetRateDaily,
          cost_rate_daily as costRateDaily,
          proposed_rate_daily as proposedRateDaily,
          created_at as createdAt,
          updated_at as updatedAt
        FROM ${this.tableName}
        WHERE estimate_id = ? AND harvest_user_id = ?
      `).get(estimateId, harvestUserId) as EstimateAllocation | undefined;

      return allocation || null;
    } catch (error) {
      console.error(`Error fetching allocation for estimate ${estimateId} and user ${harvestUserId}:`, error);
      return null;
    }
  }

  /**
   * Get allocations by Harvest user ID across all estimates
   * @param harvestUserId Harvest user ID
   * @returns Array of allocations
   */
  getAllocationsByUser(harvestUserId: number): EstimateAllocation[] {
    try {
      const allocations = this.db.prepare(`
        SELECT 
          id,
          estimate_id as estimateId,
          harvest_user_id as harvestUserId,
          first_name as firstName,
          last_name as lastName,
          project_role as projectRole,
          level,
          target_rate_daily as targetRateDaily,
          cost_rate_daily as costRateDaily,
          proposed_rate_daily as proposedRateDaily,
          created_at as createdAt,
          updated_at as updatedAt
        FROM ${this.tableName}
        WHERE harvest_user_id = ?
        ORDER BY created_at DESC
      `).all(harvestUserId) as EstimateAllocation[];

      return allocations;
    } catch (error) {
      console.error(`Error fetching allocations for user ${harvestUserId}:`, error);
      return [];
    }
  }

  /**
   * Create a new estimate allocation
   * @param allocationData Allocation data
   * @returns Created allocation
   */
  createAllocation(allocationData: EstimateAllocationCreate): EstimateAllocation {
    try {
      // Check if allocation for this estimate and user already exists
      const existing = this.getAllocationByEstimateAndUser(allocationData.estimateId, allocationData.harvestUserId);
      if (existing) {
        throw new Error(`Allocation for estimate ${allocationData.estimateId} and user ${allocationData.harvestUserId} already exists`);
      }

      const id = uuidv4();
      const now = new Date().toISOString();

      this.db.prepare(`
        INSERT INTO ${this.tableName} (
          id,
          estimate_id,
          harvest_user_id,
          first_name,
          last_name,
          project_role,
          level,
          target_rate_daily,
          cost_rate_daily,
          proposed_rate_daily,
          created_at,
          updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).run(
        id,
        allocationData.estimateId,
        allocationData.harvestUserId,
        allocationData.firstName,
        allocationData.lastName || null,
        allocationData.projectRole || null,
        allocationData.level || null,
        allocationData.targetRateDaily,
        allocationData.costRateDaily,
        allocationData.proposedRateDaily,
        now,
        now
      );

      return this.getAllocationById(id) as EstimateAllocation;
    } catch (error) {
      console.error('Error creating estimate allocation:', error);
      throw error;
    }
  }

  /**
   * Update an existing estimate allocation
   * @param id Allocation ID
   * @param updateData Update data
   * @returns Updated allocation or null if not found
   */
  updateAllocation(id: string, updateData: EstimateAllocationUpdate): EstimateAllocation | null {
    try {
      const existing = this.getAllocationById(id);
      if (!existing) {
        return null;
      }

      const now = new Date().toISOString();

      this.db.prepare(`
        UPDATE ${this.tableName}
        SET
          first_name = ?,
          last_name = ?,
          project_role = ?,
          level = ?,
          target_rate_daily = ?,
          cost_rate_daily = ?,
          proposed_rate_daily = ?,
          updated_at = ?
        WHERE id = ?
      `).run(
        updateData.firstName !== undefined ? updateData.firstName : existing.firstName,
        updateData.lastName !== undefined ? updateData.lastName : existing.lastName,
        updateData.projectRole !== undefined ? updateData.projectRole : existing.projectRole,
        updateData.level !== undefined ? updateData.level : existing.level,
        updateData.targetRateDaily !== undefined ? updateData.targetRateDaily : existing.targetRateDaily,
        updateData.costRateDaily !== undefined ? updateData.costRateDaily : existing.costRateDaily,
        updateData.proposedRateDaily !== undefined ? updateData.proposedRateDaily : existing.proposedRateDaily,
        now,
        id
      );

      return this.getAllocationById(id);
    } catch (error) {
      console.error(`Error updating allocation ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete an estimate allocation
   * @param id Allocation ID
   * @returns Boolean indicating success
   */
  deleteAllocation(id: string): boolean {
    try {
      const result = this.db.prepare(`
        DELETE FROM ${this.tableName}
        WHERE id = ?
      `).run(id);

      return (result.changes || 0) > 0;
    } catch (error) {
      console.error(`Error deleting allocation ${id}:`, error);
      return false;
    }
  }

  /**
   * Delete all allocations for an estimate
   * @param estimateId Estimate ID
   * @returns Number of deleted allocations
   */
  deleteAllocationsByEstimate(estimateId: string): number {
    try {
      const result = this.db.prepare(`
        DELETE FROM ${this.tableName}
        WHERE estimate_id = ?
      `).run(estimateId);

      return result.changes || 0;
    } catch (error) {
      console.error(`Error deleting allocations for estimate ${estimateId}:`, error);
      return 0;
    }
  }

  /**
   * Upsert allocation (create if doesn't exist, update if exists)
   * @param allocationData Allocation data
   * @returns Allocation
   */
  upsertAllocation(allocationData: EstimateAllocationCreate): EstimateAllocation {
    try {
      const existing = this.getAllocationByEstimateAndUser(allocationData.estimateId, allocationData.harvestUserId);
      
      if (existing) {
        return this.updateAllocation(existing.id, {
          firstName: allocationData.firstName,
          lastName: allocationData.lastName,
          projectRole: allocationData.projectRole,
          level: allocationData.level,
          targetRateDaily: allocationData.targetRateDaily,
          costRateDaily: allocationData.costRateDaily,
          proposedRateDaily: allocationData.proposedRateDaily
        }) as EstimateAllocation;
      } else {
        return this.createAllocation(allocationData);
      }
    } catch (error) {
      console.error('Error upserting allocation:', error);
      throw error;
    }
  }

  /**
   * Get allocation summary for an estimate
   * @param estimateId Estimate ID
   * @returns Allocation summary
   */
  getAllocationSummary(estimateId: string): {
    totalAllocations: number;
    totalTargetRate: number;
    totalCostRate: number;
    totalProposedRate: number;
    averageTargetRate: number;
    averageCostRate: number;
    averageProposedRate: number;
    totalMargin: number;
    marginPercentage: number;
  } {
    try {
      const summary = this.db.prepare(`
        SELECT 
          COUNT(*) as totalAllocations,
          SUM(target_rate_daily) as totalTargetRate,
          SUM(cost_rate_daily) as totalCostRate,
          SUM(proposed_rate_daily) as totalProposedRate,
          AVG(target_rate_daily) as averageTargetRate,
          AVG(cost_rate_daily) as averageCostRate,
          AVG(proposed_rate_daily) as averageProposedRate
        FROM ${this.tableName}
        WHERE estimate_id = ?
      `).get(estimateId) as any;

      const totalMargin = (summary.totalProposedRate || 0) - (summary.totalCostRate || 0);
      const marginPercentage = summary.totalCostRate > 0 
        ? (totalMargin / summary.totalCostRate) * 100 
        : 0;

      return {
        totalAllocations: summary.totalAllocations || 0,
        totalTargetRate: summary.totalTargetRate || 0,
        totalCostRate: summary.totalCostRate || 0,
        totalProposedRate: summary.totalProposedRate || 0,
        averageTargetRate: Math.round((summary.averageTargetRate || 0) * 100) / 100,
        averageCostRate: Math.round((summary.averageCostRate || 0) * 100) / 100,
        averageProposedRate: Math.round((summary.averageProposedRate || 0) * 100) / 100,
        totalMargin: Math.round(totalMargin * 100) / 100,
        marginPercentage: Math.round(marginPercentage * 100) / 100
      };
    } catch (error) {
      console.error(`Error fetching allocation summary for estimate ${estimateId}:`, error);
      return {
        totalAllocations: 0,
        totalTargetRate: 0,
        totalCostRate: 0,
        totalProposedRate: 0,
        averageTargetRate: 0,
        averageCostRate: 0,
        averageProposedRate: 0,
        totalMargin: 0,
        marginPercentage: 0
      };
    }
  }

  /**
   * Bulk create allocations for an estimate
   * @param estimateId Estimate ID
   * @param allocations Array of allocation data
   * @returns Array of created allocations
   */
  bulkCreateAllocations(estimateId: string, allocations: Omit<EstimateAllocationCreate, 'estimateId'>[]): EstimateAllocation[] {
    try {
      const results: EstimateAllocation[] = [];
      
      const transaction = this.db.transaction(() => {
        // First delete existing allocations for this estimate
        this.deleteAllocationsByEstimate(estimateId);
        
        // Then create new allocations
        for (const allocation of allocations) {
          const created = this.createAllocation({
            ...allocation,
            estimateId
          });
          results.push(created);
        }
      });

      transaction();
      return results;
    } catch (error) {
      console.error(`Error bulk creating allocations for estimate ${estimateId}:`, error);
      throw error;
    }
  }

  /**
   * Get unique project roles used in allocations
   * @returns Array of unique project roles
   */
  getUniqueProjectRoles(): string[] {
    try {
      const roles = this.db.prepare(`
        SELECT DISTINCT project_role
        FROM ${this.tableName}
        WHERE project_role IS NOT NULL
        ORDER BY project_role
      `).all() as { project_role: string }[];

      return roles.map(r => r.project_role);
    } catch (error) {
      console.error('Error fetching unique project roles:', error);
      return [];
    }
  }

  /**
   * Get unique levels used in allocations
   * @returns Array of unique levels
   */
  getUniqueLevels(): string[] {
    try {
      const levels = this.db.prepare(`
        SELECT DISTINCT level
        FROM ${this.tableName}
        WHERE level IS NOT NULL
        ORDER BY level
      `).all() as { level: string }[];

      return levels.map(l => l.level);
    } catch (error) {
      console.error('Error fetching unique levels:', error);
      return [];
    }
  }
}