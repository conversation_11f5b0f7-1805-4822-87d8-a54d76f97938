/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  darkMode: 'class', // Enable dark mode with class strategy
  theme: {
    screens: {
      'xs': '480px',
      'sm': '640px',
      'md': '768px',
      'lg': '1024px',
      'xl': '1280px',
      '2xl': '1536px',
    },
    extend: {
      // === CONSOLIDATED COLOR SYSTEM ===
      colors: {
        // Primary brand colors (modern blue palette)
        primary: {
          50: 'var(--color-primary-50, #eff6ff)',
          100: 'var(--color-primary-100, #dbeafe)', 
          200: 'var(--color-primary-200, #bfdbfe)',
          300: 'var(--color-primary-300, #93c5fd)',
          400: 'var(--color-primary-400, #60a5fa)',
          500: 'var(--color-primary-500, #3b82f6)', // Modern primary
          600: 'var(--color-primary-600, #2563eb)',
          700: 'var(--color-primary-700, #1d4ed8)',
          800: 'var(--color-primary-800, #1e40af)',
          900: 'var(--color-primary-900, #1e3a8a)',
          950: 'var(--color-primary-950, #172554)',
          DEFAULT: 'var(--color-primary-500, #3b82f6)',
        },
        
        // Secondary colors  
        secondary: {
          50: '#f0f9ff',
          100: '#e0f2fe',
          200: '#bae6fd',
          300: '#7dd3fc',
          400: '#38bdf8',
          500: '#4CB5F5', // Main secondary color
          600: '#0284c7',
          700: '#0369a1',
          800: '#075985',
          900: '#0c4a6e',
          DEFAULT: '#4CB5F5',
          dark: '#6B7280',
          light: '#F5EBE0',
        },

        // Error colors (modern accessible red)
        error: {
          50: 'var(--color-error-50, #fef2f2)',
          100: 'var(--color-error-100, #fee2e2)',
          200: 'var(--color-error-200, #fecaca)',
          300: 'var(--color-error-300, #fca5a5)',
          400: 'var(--color-error-400, #f87171)',
          500: 'var(--color-error-500, #ef4444)', // Modern error
          600: 'var(--color-error-600, #dc2626)',
          700: 'var(--color-error-700, #b91c1c)',
          800: 'var(--color-error-800, #991b1b)',
          900: 'var(--color-error-900, #7f1d1d)',
          DEFAULT: 'var(--color-error-500, #ef4444)'
        },
        
        // Keep accent as alias for backwards compatibility
        accent: {
          50: 'var(--color-error-50, #fef2f2)',
          100: 'var(--color-error-100, #fee2e2)',
          200: 'var(--color-error-200, #fecaca)',
          300: 'var(--color-error-300, #fca5a5)',
          400: 'var(--color-error-400, #f87171)',
          500: 'var(--color-error-500, #ef4444)',
          600: 'var(--color-error-600, #dc2626)',
          700: 'var(--color-error-700, #b91c1c)',
          800: 'var(--color-error-800, #991b1b)',
          900: 'var(--color-error-900, #7f1d1d)',
          DEFAULT: 'var(--color-error-500, #ef4444)'
        },

        success: {
          50: 'var(--color-success-50, #f0fdf4)',
          100: 'var(--color-success-100, #dcfce7)',
          200: 'var(--color-success-200, #bbf7d0)',
          300: 'var(--color-success-300, #86efac)',
          400: 'var(--color-success-400, #4ade80)',
          500: 'var(--color-success-500, #10b981)', // Modern success
          600: 'var(--color-success-600, #059669)',
          700: 'var(--color-success-700, #047857)',
          800: 'var(--color-success-800, #065f46)',
          900: 'var(--color-success-900, #064e3b)',
          DEFAULT: 'var(--color-success-500, #10b981)'
        },

        warning: {
          50: 'var(--color-warning-50, #fffbeb)',
          100: 'var(--color-warning-100, #fef3c7)',
          200: 'var(--color-warning-200, #fde68a)',
          300: 'var(--color-warning-300, #fcd34d)',
          400: 'var(--color-warning-400, #fbbf24)',
          500: 'var(--color-warning-500, #f59e0b)', // Modern warning
          600: 'var(--color-warning-600, #d97706)',
          700: 'var(--color-warning-700, #b45309)',
          800: 'var(--color-warning-800, #92400e)',
          900: 'var(--color-warning-900, #78350f)',
          DEFAULT: 'var(--color-warning-500, #f59e0b)'
        },
        
        // Modern gray palette
        gray: {
          50: 'var(--color-gray-50, #f9fafb)',
          100: 'var(--color-gray-100, #f3f4f6)',
          200: 'var(--color-gray-200, #e5e7eb)',
          300: 'var(--color-gray-300, #d1d5db)',
          400: 'var(--color-gray-400, #9ca3af)',
          500: 'var(--color-gray-500, #6b7280)',
          600: 'var(--color-gray-600, #4b5563)',
          700: 'var(--color-gray-700, #374151)',
          800: 'var(--color-gray-800, #1f2937)',
          900: 'var(--color-gray-900, #111827)',
          950: 'var(--color-gray-950, #030712)',
        },

        // Chart specific colors
        chart: {
          actual: '#3498db',
          projected: '#f39c12',
          income: '#27ae60',
          expense: '#e74c3c'
        },

        // Semantic background colors
        background: {
          DEFAULT: '#f9f9f9',
          dark: '#121212',
          card: '#ffffff',
          'card-dark': '#1f2937'
        },

        // Border colors
        border: {
          DEFAULT: '#ddd',
          dark: '#333',
          light: '#e5e7eb'
        },

        // Text colors
        text: {
          DEFAULT: '#333',
          dark: '#e0e0e0',
          muted: '#777',
          'muted-dark': '#9ca3af'
        },

        // Legacy color mappings for blue
        blue: {
          logo: '#2870AB',
        }
      },

      // === FLUID TYPOGRAPHY SYSTEM ===
      // Consolidated from short-term-cashflow.css CSS variables
      fontSize: {
        'fluid-xs': 'clamp(0.65rem, 0.5vw + 0.5rem, 0.75rem)',
        'fluid-sm': 'clamp(0.75rem, 0.75vw + 0.5rem, 0.875rem)', 
        'fluid-base': 'clamp(0.875rem, 1vw + 0.5rem, 1rem)',
        'fluid-lg': 'clamp(1rem, 1.25vw + 0.5rem, 1.125rem)',
        'fluid-xl': 'clamp(1.125rem, 1.5vw + 0.5rem, 1.25rem)',
        'fluid-2xl': 'clamp(1.25rem, 2vw + 0.5rem, 1.5rem)',
      },

      // === FLUID SPACING SYSTEM ===
      // Consolidated from short-term-cashflow.css CSS variables
      spacing: {
        'fluid-xs': 'clamp(0.25rem, 0.25vw + 0.25rem, 0.5rem)',
        'fluid-sm': 'clamp(0.5rem, 0.5vw + 0.25rem, 0.75rem)',
        'fluid-md': 'clamp(0.75rem, 0.75vw + 0.25rem, 1rem)', 
        'fluid-lg': 'clamp(1rem, 1vw + 0.5rem, 1.5rem)',
        'fluid-xl': 'clamp(1.5rem, 1.5vw + 0.5rem, 2rem)',
      },

      // === FLUID WIDTH SYSTEM ===
      // Consolidated from short-term-cashflow.css CSS variables
      maxWidth: {
        'fluid-xs': 'clamp(16rem, 30vw, 20rem)',
        'fluid-sm': 'clamp(20rem, 40vw, 24rem)',
        'fluid-md': 'clamp(24rem, 60vw, 28rem)',
        'fluid-lg': 'clamp(28rem, 80vw, 32rem)',
        'fluid-xl': 'clamp(32rem, 90vw, 36rem)',
      },

      // === ENHANCED BOX SHADOWS ===
      boxShadow: {
        'xs': 'var(--shadow-xs, 0 1px 2px rgba(0, 0, 0, 0.05))',
        'sm': 'var(--shadow-sm, 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06))',
        'md': 'var(--shadow-md, 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06))',
        'lg': 'var(--shadow-lg, 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05))',
        'xl': 'var(--shadow-xl, 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04))',
        '2xl': 'var(--shadow-2xl, 0 25px 50px -12px rgba(0, 0, 0, 0.25))',
        'inner': 'var(--shadow-inner, inset 0 2px 4px 0 rgba(0, 0, 0, 0.06))',
        // Legacy shadows for backwards compatibility
        card: 'var(--shadow-sm)',
        'card-hover': 'var(--shadow-lg)',
        'card-dark': 'var(--shadow-md)',
        'floating': '0 10px 25px rgba(0, 0, 0, 0.15)',
      },

      // === TYPOGRAPHY ===
      fontFamily: {
        sans: ['Inter', 'ui-sans-serif', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'sans-serif'],
      },

      // === ENHANCED ANIMATIONS ===
      animation: {
        fadeIn: 'fadeIn 0.5s ease-in-out',
        'slide-in': 'slideIn 0.3s ease-out forwards',
        'slide-in-left': 'slideInLeft 0.3s ease-out forwards',
        'slide-out': 'slideOut 0.3s ease-in forwards',
        'slide-out-left': 'slideOutLeft 0.3s ease-in forwards',
        'spin-slow': 'spin 3s linear infinite',
        'spin-reverse': 'spin-reverse 2.5s linear infinite',
        'pulse': 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'ellipsis1': 'ellipsis 1.4s infinite 0s',
        'ellipsis2': 'ellipsis 1.4s infinite 0.2s',
        'ellipsis3': 'ellipsis 1.4s infinite 0.4s',
        'progress-indeterminate': 'progress-indeterminate 1.5s ease-in-out infinite',
        // New smooth transitions
        'scale-in': 'scale-in 0.2s ease-out',
        'scale-out': 'scale-out 0.15s ease-in',
      },

      keyframes: {
        fadeIn: {
          '0%': { opacity: 0 },
          '100%': { opacity: 1 },
        },
        slideIn: {
          '0%': { transform: 'translateX(100%)' },
          '100%': { transform: 'translateX(0)' },
        },
        slideInLeft: {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(0)' },
        },
        slideOut: {
          '0%': { transform: 'translateX(0)' },
          '100%': { transform: 'translateX(100%)' },
        },
        slideOutLeft: {
          '0%': { transform: 'translateX(0)' },
          '100%': { transform: 'translateX(-100%)' },
        },
        'spin-reverse': {
          '0%': { transform: 'rotate(0deg)' },
          '100%': { transform: 'rotate(-360deg)' },
        },
        ellipsis: {
          '0%': { opacity: 0 },
          '50%': { opacity: 1 },
          '100%': { opacity: 0 },
        },
        'progress-indeterminate': {
          '0%': { transform: 'translateX(-100%)' },
          '50%': { transform: 'translateX(0%)' },
          '100%': { transform: 'translateX(100%)' },
        },
        'scale-in': {
          '0%': { transform: 'scale(0.95)', opacity: 0 },
          '100%': { transform: 'scale(1)', opacity: 1 },
        },
        'scale-out': {
          '0%': { transform: 'scale(1)', opacity: 1 },
          '100%': { transform: 'scale(0.95)', opacity: 0 },
        },
      },

      // === ENHANCED TRANSITIONS ===
      transitionProperty: {
        'height': 'height',
        'spacing': 'margin, padding',
        'colors': 'color, background-color, border-color, text-decoration-color, fill, stroke',
        'opacity': 'opacity',
        'shadow': 'box-shadow',
        'transform': 'transform',
      },

      transitionDuration: {
        '250': '250ms',
        '400': '400ms',
      },

      transitionTimingFunction: {
        'out-expo': 'cubic-bezier(0.16, 1, 0.3, 1)',
        'in-expo': 'cubic-bezier(0.7, 0, 0.84, 0)',
      },
    },
  },
  plugins: [
    require('@tailwindcss/container-queries'),
  ],
}