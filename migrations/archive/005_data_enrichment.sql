-- Migration: Add data enrichment tables
-- Purpose: Store enriched data from external sources (ABN Lookup, etc.) separately from core entities
-- Date: 2025-01-06

-- Table to store enriched company data from various sources
CREATE TABLE IF NOT EXISTS company_enrichment (
  id TEXT PRIMARY KEY,
  company_id TEXT NOT NULL,
  source TEXT NOT NULL, -- 'abn_lookup', 'clearbit', 'apollo', etc.
  source_id TEXT, -- External ID from the source (e.g., ABN number)
  data JSON NOT NULL, -- Full enriched data as JSON
  confidence_score REAL DEFAULT 1.0, -- 0.0 to 1.0
  enriched_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  expires_at TIMESTAMP, -- When to re-enrich
  created_by TEXT DEFAULT 'system',
  FOREIGN KEY (company_id) REFERENCES company(id) ON DELETE CASCADE
);

-- Table to store enriched contact data from various sources
CREATE TABLE IF NOT EXISTS contact_enrichment (
  id TEXT PRIMARY KEY,
  contact_id TEXT NOT NULL,
  source TEXT NOT NULL, -- 'clearbit', 'apollo', 'linkedin', etc.
  source_id TEXT, -- External ID from the source
  data JSON NOT NULL, -- Full enriched data as JSON
  confidence_score REAL DEFAULT 1.0, -- 0.0 to 1.0
  enriched_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  expires_at TIMESTAMP, -- When to re-enrich
  created_by TEXT DEFAULT 'system',
  FOREIGN KEY (contact_id) REFERENCES contact(id) ON DELETE CASCADE
);

-- Table to track enrichment attempts and failures
CREATE TABLE IF NOT EXISTS enrichment_log (
  id TEXT PRIMARY KEY,
  entity_type TEXT NOT NULL, -- 'company' or 'contact'
  entity_id TEXT NOT NULL,
  source TEXT NOT NULL,
  status TEXT NOT NULL, -- 'success', 'failed', 'no_match', 'rate_limited'
  error_message TEXT,
  attempted_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  response_time_ms INTEGER,
  api_credits_used INTEGER DEFAULT 0
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_company_enrichment_company_id ON company_enrichment(company_id);
CREATE INDEX IF NOT EXISTS idx_company_enrichment_source ON company_enrichment(source);
CREATE INDEX IF NOT EXISTS idx_company_enrichment_expires_at ON company_enrichment(expires_at);

CREATE INDEX IF NOT EXISTS idx_contact_enrichment_contact_id ON contact_enrichment(contact_id);
CREATE INDEX IF NOT EXISTS idx_contact_enrichment_source ON contact_enrichment(source);
CREATE INDEX IF NOT EXISTS idx_contact_enrichment_expires_at ON contact_enrichment(expires_at);

CREATE INDEX IF NOT EXISTS idx_enrichment_log_entity ON enrichment_log(entity_type, entity_id);
CREATE INDEX IF NOT EXISTS idx_enrichment_log_attempted_at ON enrichment_log(attempted_at);

-- Add enrichment status fields to company table (non-breaking)
-- These are denormalized for quick filtering in the UI
ALTER TABLE company ADD COLUMN enrichment_status JSON;
ALTER TABLE company ADD COLUMN last_enriched_at TIMESTAMP;

-- Add enrichment status fields to contact table (non-breaking)
ALTER TABLE contact ADD COLUMN enrichment_status JSON;
ALTER TABLE contact ADD COLUMN last_enriched_at TIMESTAMP;