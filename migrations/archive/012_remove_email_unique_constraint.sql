-- Remove UNIQUE constraint from contact.email
-- This allows multiple contacts to have the same email address
-- Similar to how multiple companies can have the same name

-- SQLite doesn't support DROP CONSTRAINT, so we need to:
-- 1. Create a new table without the constraint
-- 2. Copy data from the old table
-- 3. Drop the old table
-- 4. Rename the new table

-- Create new contact table without email UNIQUE constraint
CREATE TABLE IF NOT EXISTS contact_new (
  id TEXT PRIMARY KEY,
  first_name TEXT NOT NULL,
  last_name TEXT,
  email TEXT, -- No UNIQUE constraint
  phone TEXT,
  job_title TEXT,
  
  /* External System IDs */
  hubspot_id TEXT UNIQUE,
  harvest_user_id TEXT UNIQUE,
  
  /* Source and Status Information */
  source TEXT,
  deleted_at TEXT,
  
  /* Audit Information */
  created_at TEXT NOT NULL,
  updated_at TEXT NOT NULL,
  created_by TEXT,
  updated_by TEXT,
  notes TEXT,
  
  /* Enrichment Fields */
  enrichment_status JSON,
  last_enriched_at TIMESTAMP
);

-- Copy data from the old table, handling duplicate hubspot_ids
-- For duplicate hubspot_ids, only keep the first one (by rowid)
INSERT INTO contact_new 
SELECT 
  id,
  first_name,
  last_name,
  email,
  phone,
  job_title,
  CASE 
    WHEN hubspot_id IS NOT NULL AND hubspot_id IN (
      SELECT hubspot_id 
      FROM contact c2 
      WHERE c2.hubspot_id = contact.hubspot_id 
      AND c2.rowid < contact.rowid
    ) THEN NULL
    ELSE hubspot_id
  END AS hubspot_id,
  harvest_user_id,
  source,
  deleted_at,
  created_at,
  updated_at,
  created_by,
  updated_by,
  notes,
  enrichment_status,
  last_enriched_at
FROM contact;

-- Drop the old table
DROP TABLE contact;

-- Rename the new table to contact
ALTER TABLE contact_new RENAME TO contact;

-- Recreate all indexes
CREATE INDEX IF NOT EXISTS idx_contact_name ON contact(first_name, last_name);
CREATE INDEX IF NOT EXISTS idx_contact_email ON contact(email);
CREATE INDEX IF NOT EXISTS idx_contact_hubspot_id ON contact(hubspot_id);
CREATE INDEX IF NOT EXISTS idx_contact_harvest_user_id ON contact(harvest_user_id);
CREATE INDEX IF NOT EXISTS idx_contact_source ON contact(source);
CREATE INDEX IF NOT EXISTS idx_contact_deleted_at ON contact(deleted_at);