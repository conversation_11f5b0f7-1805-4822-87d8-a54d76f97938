/* Main import file for all component-specific CSS */

/* Import card styles */
@import "./card.css";

/* Import transaction styles */
@import "./transaction-card.css";
@import "./transactions-list.css";

/* Import other component styles */
@import "./chart-tooltip.css";
@import "./chart-responsive.css"; /* Added for better chart mobile display */
@import "./cashflow-chart.css"; /* Added for CashflowChart component */
@import "./cashflow-summary-card.css"; /* Added for CashflowSummaryCard component */
@import "./cashflow-grid.css"; /* Added for financial summary cards grid layout */
@import "./unified-navigation.css"; /* Updated to use unified navigation styles */
@import "./decision-card.css";
@import "./xero-integration.css";
@import "./tax-calendar.css";
@import "./crm.css"; /* CRM component styles */
@import "./radar.css"; /* Radar component styles */
@import "./deal-edit.css"; /* Deal Edit page styles */
@import "./data-management.css"; /* Data Management table styles */

/* Floating Panels Responsive Styles */
.floating-actions-panel {
  z-index: 60;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.floating-finance-panel {
  z-index: 58;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

/* Container for floating panels */
.floating-panels-container {
  position: relative;
  height: 36px; /* Minimum height to avoid page jumping */
  margin-top: 16px;
}

/* Panel transitions */
.panel-enter {
  opacity: 0;
  transform: translateY(10px);
}

.panel-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.3s, transform 0.3s;
}

.panel-exit {
  opacity: 1;
}

.panel-exit-active {
  opacity: 0;
  transform: translateY(10px);
  transition: opacity 0.3s, transform 0.3s;
}

/* Add animation class for the collapse button */
.collapse-btn {
  transition: transform 0.3s ease, background-color 0.2s ease;
}

.collapse-btn:hover {
  transform: scale(1.1);
}

/* We've simplified all positioning to use fixed positioning directly */

/* Simple fixed positioning for panels */
.floating-finance-panel,
.floating-actions-panel {
  right: 16px;
  width: 220px; /* Use fixed width for consistent sizing */
}

.floating-finance-panel {
  bottom: 210px; /* Increased space above the actions panel */
}

.floating-actions-panel {
  bottom: 16px;
}

/* Help button completely outside the panel */
.help-button-outside {
  bottom: 148px; /* Align with top of the action panel */
  right: 238px; /* To the left of the action panel */
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 9999px;
  background-color: rgba(209, 213, 219, 0.8);
  color: rgb(75, 85, 99);
  font-size: 12px;
  z-index: 65;
}

.help-tooltip-outside {
  bottom: 145px; /* Match button position */
  right: 248px;
  width: 240px;
  padding: 8px 12px;
  background-color: white;
  border: 1px solid rgb(229, 231, 235);
  border-radius: 6px;
  font-size: 12px;
  color: rgb(75, 85, 99);
  z-index: 70;
}

.dark .help-button-outside {
  background-color: rgba(75, 85, 99, 0.6);
  color: rgb(209, 213, 219);
}

.dark .help-tooltip-outside {
  background-color: rgb(31, 41, 55);
  border-color: rgb(75, 85, 99);
  color: rgb(209, 213, 219);
}

@media (max-width: 640px) {
  .floating-actions-panel,
  .floating-finance-panel {
    right: 1rem; /* Match smaller screen content padding */
    width: 200px;
  }
}

@media (max-width: 480px) {
  .floating-actions-panel,
  .floating-finance-panel {
    width: 190px;
  }
}
