-- CRM Relationship Enhancements Migration
-- Adds contact-to-contact relationships and conversation threading support

-- Enable foreign keys
PRAGMA foreign_keys = ON;

-- ============================================
-- 1. Contact Relationships Table
-- ============================================
-- Tracks how contacts know each other across organizations
CREATE TABLE IF NOT EXISTS contact_relationships (
    id TEXT PRIMARY KEY,
    source_contact_id TEXT NOT NULL,
    target_contact_id TEXT NOT NULL,
    relationship_type TEXT NOT NULL, -- 'knows', 'reports_to', 'introduced_by', 'worked_with', 'colleague'
    strength INTEGER DEFAULT 1 CHECK(strength >= 1 AND strength <= 5), -- 1-5 scale
    context TEXT, -- Free text description of the relationship
    created_at TEXT NOT NULL DEFAULT (datetime('now')),
    created_by TEXT,
    updated_at TEXT DEFAULT (datetime('now')),
    updated_by TEXT,
    FOREIGN KEY (source_contact_id) REFERENCES contact(id) ON DELETE CASCADE,
    FOREIGN KEY (target_contact_id) REFERENCES contact(id) ON DELETE CASCADE,
    -- Ensure no duplicate relationships
    UNIQUE(source_contact_id, target_contact_id, relationship_type)
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_contact_relationships_source ON contact_relationships(source_contact_id);
CREATE INDEX IF NOT EXISTS idx_contact_relationships_target ON contact_relationships(target_contact_id);
CREATE INDEX IF NOT EXISTS idx_contact_relationships_type ON contact_relationships(relationship_type);

-- ============================================
-- 2. Note Table Enhancements for Threading
-- ============================================
-- Add columns to support conversation threading
ALTER TABLE note ADD COLUMN parent_note_id TEXT REFERENCES note(id) ON DELETE SET NULL;
ALTER TABLE note ADD COLUMN thread_id TEXT; -- Groups related conversations
ALTER TABLE note ADD COLUMN participants TEXT; -- JSON array of contact IDs
ALTER TABLE note ADD COLUMN conversation_type TEXT CHECK(conversation_type IN ('email', 'call', 'meeting', 'slack', 'internal', NULL));
ALTER TABLE note ADD COLUMN status TEXT DEFAULT 'open' CHECK(status IN ('open', 'resolved', 'parked', 'archived'));

-- Indexes for conversation threading
CREATE INDEX IF NOT EXISTS idx_note_thread ON note(thread_id);
CREATE INDEX IF NOT EXISTS idx_note_parent ON note(parent_note_id);
CREATE INDEX IF NOT EXISTS idx_note_status ON note(status);
CREATE INDEX IF NOT EXISTS idx_note_conversation_type ON note(conversation_type);

-- ============================================
-- 3. Team Coverage Tracking
-- ============================================
-- Track which team members have relationships with which contacts
CREATE TABLE IF NOT EXISTS team_contact_coverage (
    id TEXT PRIMARY KEY,
    contact_id TEXT NOT NULL,
    team_member_id TEXT NOT NULL, -- References Harvest user ID
    relationship_strength TEXT CHECK(relationship_strength IN ('primary', 'secondary', 'minimal')),
    last_interaction_date TEXT,
    last_interaction_type TEXT,
    notes TEXT,
    created_at TEXT NOT NULL DEFAULT (datetime('now')),
    updated_at TEXT DEFAULT (datetime('now')),
    FOREIGN KEY (contact_id) REFERENCES contact(id) ON DELETE CASCADE,
    UNIQUE(contact_id, team_member_id)
);

CREATE INDEX IF NOT EXISTS idx_team_coverage_contact ON team_contact_coverage(contact_id);
CREATE INDEX IF NOT EXISTS idx_team_coverage_member ON team_contact_coverage(team_member_id);

-- ============================================
-- 4. Opportunity Intelligence
-- ============================================
-- Track identified opportunities and their sources
CREATE TABLE IF NOT EXISTS opportunity_intelligence (
    id TEXT PRIMARY KEY,
    entity_type TEXT NOT NULL CHECK(entity_type IN ('contact', 'company', 'deal')),
    entity_id TEXT NOT NULL,
    opportunity_type TEXT NOT NULL CHECK(opportunity_type IN ('expansion', 'referral', 'introduction', 'upsell', 'cross_sell')),
    description TEXT NOT NULL,
    confidence_level INTEGER DEFAULT 3 CHECK(confidence_level >= 1 AND confidence_level <= 5),
    estimated_value REAL,
    timing TEXT, -- When to pursue
    source TEXT, -- How we identified this opportunity
    status TEXT DEFAULT 'identified' CHECK(status IN ('identified', 'qualified', 'pursuing', 'won', 'lost', 'deferred')),
    created_at TEXT NOT NULL DEFAULT (datetime('now')),
    created_by TEXT,
    updated_at TEXT DEFAULT (datetime('now')),
    updated_by TEXT
);

CREATE INDEX IF NOT EXISTS idx_opportunity_entity ON opportunity_intelligence(entity_type, entity_id);
CREATE INDEX IF NOT EXISTS idx_opportunity_status ON opportunity_intelligence(status);
CREATE INDEX IF NOT EXISTS idx_opportunity_type ON opportunity_intelligence(opportunity_type);

-- ============================================
-- 5. Migration Tracking
-- ============================================
INSERT INTO migrations (id, name, batch) 
VALUES ('002', '002_crm_relationship_enhancements', 2);