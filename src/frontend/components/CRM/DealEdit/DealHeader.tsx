import React from "react";
import { Deal, DealUpdate, DealStage } from "../../../types/crm-types";
import {
  DocumentTextIcon,
  CurrencyDollarIcon,
  ArrowTopRightOnSquareIcon,
  LinkIcon,
  BanknotesIcon,
  ChartBarIcon,
} from "@heroicons/react/24/outline";
import {
  getFirstLinkedEstimate,
  getEstimateUrl,
  isFieldControlledByEstimate,
} from "./utils";
import DealStageProgress from "./DealStageProgress";

interface DealHeaderProps {
  deal: Deal;
  isEditing: boolean;
  formData: DealUpdate;
  onChange: (field: keyof DealUpdate, value: any) => void;
  onEdit: () => void;
  onSave: (e: React.FormEvent) => void;
  onCancel: () => void;
  onBack: () => void;
  isLoading: boolean;
}

/**
 * Header component for the Deal Edit Page
 */
const DealHeader: React.FC<DealHeaderProps> = ({
  deal,
  isEditing,
  formData,
  onChange,
  onEdit,
  onSave,
  onCancel,
  onBack,
  isLoading,
}) => {
  // Format currency value
  const formatCurrency = (value?: number, currency?: string): string => {
    if (value === undefined) return "N/A";

    return new Intl.NumberFormat("en-AU", {
      style: "currency",
      currency: currency || "AUD",
      maximumFractionDigits: 0,
    }).format(value);
  };

  // Format probability as percentage
  const formatProbability = (probability?: number): string => {
    if (probability === undefined) return "N/A";
    return `${Math.round(probability * 100)}%`;
  };

  // Deal stages for dropdown
  const DEAL_STAGES: DealStage[] = [
    "Identified",
    "Qualified",
    "Solution proposal",
    "Solution presentation",
    "Objection handling",
    "Finalising terms",
    "Closed won",
    "Closed lost",
    "Abandoned",
  ];

  return (
    <div className="p-6">
      {/* Navigation and Action Buttons */}
      <div className="flex justify-between items-center mb-6">
        <button
          onClick={onBack}
          className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
        >
          <svg
            className="w-5 h-5 mr-1"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M10 19l-7-7m0 0l7-7m-7 7h18"
            />
          </svg>
          Back to CRM
        </button>

        <div className="flex space-x-2">
          {isEditing ? (
            <>
              <button
                onClick={onCancel}
                className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600"
                disabled={isLoading}
              >
                Cancel
              </button>
              <button
                onClick={onSave}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-blue-500 dark:hover:bg-blue-600"
                disabled={isLoading}
              >
                {isLoading ? (
                  <span className="flex items-center">
                    <svg
                      className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    Saving...
                  </span>
                ) : (
                  "Save Changes"
                )}
              </button>
            </>
          ) : (
            <>
              <button
                onClick={onEdit}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-blue-500 dark:hover:bg-blue-600"
              >
                Edit Deal
              </button>
            </>
          )}
        </div>
      </div>

      {/* Deal Title Section */}
      <div className="mb-6">
        {console.log('DealHeader render:', { isEditing, deal, fieldOwnership })}
        {isEditing ? (
          <div>
            {deal.hubspotId && (
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Deal Name
                <span className="ml-2 inline-flex items-center text-xs text-blue-600 dark:text-blue-400">
                  <LinkIcon className="w-3 h-3 mr-1" />
                  Synced from HubSpot
                </span>
              </label>
            )}
            <input
              type="text"
              value={formData.name || ""}
              onChange={(e) => onChange("name", e.target.value)}
              className={`block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 text-2xl dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                isFieldControlledByEstimate(deal, "name", fieldOwnership) ? 'bg-gray-50 dark:bg-gray-800 cursor-not-allowed' : ''
              }`}
              placeholder="Deal Name"
              disabled={(() => {
                const isControlled = isFieldControlledByEstimate(deal, "name", fieldOwnership);
                console.log('Deal name field disabled check:', {
                  isControlled,
                  dealId: deal.id,
                  dealEstimates: deal.estimates,
                  fieldOwnership,
                  fieldOwnershipForName: fieldOwnership?.name
                });
                return isControlled;
              })()}
            />
            {deal.hubspotId && deal.hubspot_name && deal.hubspot_name !== deal.name && (
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                HubSpot name: {deal.hubspot_name}
              </p>
            )}
          </div>
        ) : (
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            {deal.name}
            {deal.hubspotId && (
              <span className="ml-3 inline-flex items-center text-sm text-blue-600 dark:text-blue-400">
                <LinkIcon className="w-4 h-4 mr-1" />
                HubSpot
              </span>
            )}
          </h1>
        )}
      </div>

      {/* Key Deal Information */}
      <div className="flex flex-wrap gap-3 mb-6">
        {/* Deal Stage Badge */}
        {!isEditing && (
          <div
            className={`
              inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium
              ${
                deal.stage === "Closed won"
                  ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                  : deal.stage === "Closed lost" || deal.stage === "Abandoned"
                  ? "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
                  : "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
              }
            `}
          >
            {deal.stage}
          </div>
        )}

        {/* Deal Value */}
        <div className="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
          <BanknotesIcon className="w-4 h-4 mr-1.5" />
          {formatCurrency(deal.value, deal.currency)}
          {!isEditing && isFieldControlledByEstimate(deal, "value") && (
            <LinkIcon className="w-3.5 h-3.5 ml-1 text-blue-500 dark:text-blue-400" title="Controlled by estimate" />
          )}
          {!isEditing && deal.hubspotId && !isFieldControlledByEstimate(deal, "value") && (
            <LinkIcon className="w-3.5 h-3.5 ml-1 text-blue-500 dark:text-blue-400" title="Synced from HubSpot" />
          )}
        </div>

        {/* Deal Probability */}
        <div className="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
          <ChartBarIcon className="w-4 h-4 mr-1.5" />
          {formatProbability(deal.probability)}
        </div>

        {/* Estimate badge */}
        {!isEditing && deal.estimates && deal.estimates.length > 0 && (
          <div className="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-200">
            <DocumentTextIcon className="w-4 h-4 mr-1.5" />
            {deal.estimates.length === 1
              ? "1 Linked Estimate"
              : `${deal.estimates.length} Linked Estimates`}
          </div>
        )}
      </div>

      {/* Stage Dropdown when editing */}
      {isEditing && (
        <div className="mb-6">
          <label
            htmlFor="dealStage"
            className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
          >
            Deal Stage
          </label>
          <select
            id="dealStage"
            value={formData.stage || ""}
            onChange={(e) => onChange("stage", e.target.value)}
            className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          >
            {DEAL_STAGES.map((stage) => (
              <option key={stage} value={stage}>
                {stage}
              </option>
            ))}
          </select>
        </div>
      )}

      {/* Deal Editing Form - Additional fields when in edit mode */}
      {isEditing && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          {/* Deal Value input fields */}
          <div>
            <label
              htmlFor="dealValue"
              className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
            >
              Deal Value
              {isFieldControlledByEstimate(deal, "value") && (
                <span className="ml-2 inline-flex items-center text-xs text-blue-600 dark:text-blue-400">
                  <LinkIcon className="w-3 h-3 mr-1" />
                  Controlled by estimate
                </span>
              )}
              {!isFieldControlledByEstimate(deal, "value") && deal.hubspotId && (
                <span className="ml-2 inline-flex items-center text-xs text-blue-600 dark:text-blue-400">
                  <LinkIcon className="w-3 h-3 mr-1" />
                  Synced from HubSpot
                </span>
              )}
            </label>

            {isFieldControlledByEstimate(deal, "value") || deal.hubspotId ? (
              <div>
                <div className="flex-1 text-sm text-gray-900 dark:text-gray-200 py-2 px-3 border border-blue-300 dark:border-blue-700 rounded-md bg-gray-50 dark:bg-gray-800">
                  {formatCurrency(deal.value, deal.currency)}
                </div>
                {deal.hubspotId && deal.hubspot_value !== undefined && deal.hubspot_value !== deal.value && (
                  <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    HubSpot value: {formatCurrency(deal.hubspot_value, deal.currency)}
                  </p>
                )}
              </div>
            ) : (
              <div className="flex">
                <select
                  id="dealCurrency"
                  value={formData.currency || "AUD"}
                  onChange={(e) => onChange("currency", e.target.value)}
                  className="block w-20 border-gray-300 rounded-l-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                >
                  <option value="AUD">AUD</option>
                  <option value="USD">USD</option>
                  <option value="EUR">EUR</option>
                  <option value="GBP">GBP</option>
                </select>
                <input
                  id="dealValue"
                  type="number"
                  value={formData.value || ""}
                  onChange={(e) =>
                    onChange(
                      "value",
                      e.target.value === ""
                        ? undefined
                        : parseFloat(e.target.value)
                    )
                  }
                  className="block w-full border-gray-300 rounded-r-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  placeholder="Value"
                />
              </div>
            )}
          </div>

          {/* Deal Probability input field */}
          <div>
            <label
              htmlFor="dealProbability"
              className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
            >
              Probability
            </label>
            <div className="flex">
              <input
                id="dealProbability"
                type="number"
                min="0"
                max="100"
                value={
                  formData.probability !== undefined
                    ? Math.round(formData.probability * 100)
                    : ""
                }
                onChange={(e) =>
                  onChange(
                    "probability",
                    e.target.value === ""
                      ? undefined
                      : parseFloat(e.target.value) / 100
                  )
                }
                className="block w-full border-gray-300 rounded-l-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                placeholder="0-100"
              />
              <div className="inline-flex items-center justify-center min-w-[40px] border border-l-0 border-gray-300 rounded-r-md bg-gray-50 text-gray-500 dark:bg-gray-700 dark:text-gray-400 dark:border-gray-600">
                %
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Deal Stage Progress - only displayed when not editing */}
      {!isEditing && <DealStageProgress currentStage={deal.stage} />}
    </div>
  );
};

export default DealHeader;
