/**
 * Activity Item Component
 *
 * This component displays an individual activity with appropriate icons,
 * user information, and metadata.
 */

import React, { useState } from 'react';
import { Activity, ActivityType, ActivitySource } from '../../types/activity-types';

interface ActivityItemProps {
  activity: Activity;
  showConnector?: boolean;
}

/**
 * Activity Item component
 */
const ActivityItem: React.FC<ActivityItemProps> = ({ activity, showConnector = false }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  // Get icon for activity type
  const getActivityIcon = (type: ActivityType, source: ActivitySource) => {
    const iconClass = "h-5 w-5";
    
    // System/Integration activities
    if (source === 'hubspot') {
      return (
        <div className="h-8 w-8 rounded-full bg-orange-100 dark:bg-orange-900 flex items-center justify-center">
          <svg className={`${iconClass} text-orange-600 dark:text-orange-400`} fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
          </svg>
        </div>
      );
    }

    if (source === 'xero') {
      return (
        <div className="h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
          <svg className={`${iconClass} text-blue-600 dark:text-blue-400`} fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
          </svg>
        </div>
      );
    }

    if (source === 'harvest') {
      return (
        <div className="h-8 w-8 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center">
          <svg className={`${iconClass} text-green-600 dark:text-green-400`} fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
          </svg>
        </div>
      );
    }

    if (source === 'system') {
      return (
        <div className="h-8 w-8 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
          <svg className={`${iconClass} text-gray-600 dark:text-gray-400`} fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
          </svg>
        </div>
      );
    }

    // User activities
    if (type.includes('deal')) {
      return (
        <div className="h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
          <svg className={`${iconClass} text-blue-600 dark:text-blue-400`} fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clipRule="evenodd" />
            <path d="M2 13.692V16a2 2 0 002 2h12a2 2 0 002-2v-2.308A24.974 24.974 0 0110 15c-2.796 0-5.487-.46-8-1.308z" />
          </svg>
        </div>
      );
    }

    if (type.includes('estimate')) {
      return (
        <div className="h-8 w-8 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center">
          <svg className={`${iconClass} text-purple-600 dark:text-purple-400`} fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clipRule="evenodd" />
          </svg>
        </div>
      );
    }

    if (type.includes('company')) {
      return (
        <div className="h-8 w-8 rounded-full bg-indigo-100 dark:bg-indigo-900 flex items-center justify-center">
          <svg className={`${iconClass} text-indigo-600 dark:text-indigo-400`} fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm3 5a1 1 0 011-1h4a1 1 0 110 2H8a1 1 0 01-1-1zm0 3a1 1 0 011-1h4a1 1 0 110 2H8a1 1 0 01-1-1z" clipRule="evenodd" />
          </svg>
        </div>
      );
    }

    if (type.includes('note')) {
      return (
        <div className="h-8 w-8 rounded-full bg-yellow-100 dark:bg-yellow-900 flex items-center justify-center">
          <svg className={`${iconClass} text-yellow-600 dark:text-yellow-400`} fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
          </svg>
        </div>
      );
    }

    // Default icon
    return (
      <div className="h-8 w-8 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
        <svg className={`${iconClass} text-gray-600 dark:text-gray-400`} fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
        </svg>
      </div>
    );
  };

  // Format timestamp
  const formatTime = (dateString: string): string => {
    return new Date(dateString).toLocaleTimeString('en-AU', {
      hour: 'numeric',
      minute: '2-digit',
    });
  };

  // Get importance color
  const getImportanceColor = (importance: string) => {
    switch (importance) {
      case 'high':
        return 'text-red-600 dark:text-red-400';
      case 'low':
        return 'text-gray-500 dark:text-gray-400';
      default:
        return 'text-gray-700 dark:text-gray-300';
    }
  };

  // Render metadata in a user-friendly way
  const renderMetadata = (metadata: Record<string, any>) => {
    const entries = Object.entries(metadata);
    
    // If there are only syncType and importedCount, keep them for sync activities
    // since showing some details is better than showing nothing
    const filteredEntries = entries.filter(([key, value]) => {
      // Skip null/undefined values
      if (value === null || value === undefined) return false;
      
      // For sync activities, if we only have syncType and importedCount, keep them
      if (activity.type.includes('sync')) {
        const onlyBasicSyncData = entries.length <= 2 && 
          entries.every(([k]) => k === 'syncType' || k === 'importedCount');
        
        if (onlyBasicSyncData) {
          return true; // Keep everything when there's limited data
        } else {
          // Otherwise filter out redundant data when there's more info
          return !(key === 'syncType' || key === 'importedCount');
        }
      }
      
      return true;
    });
    
    return filteredEntries.map(([key, value]) => {
      
      // Format key to be more readable
      const formatKey = (k: string) => {
        return k
          .split(/(?=[A-Z])|[_-]/)
          .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
          .join(' ');
      };
      
      // Format value based on type
      const formatValue = (v: any) => {
        if (typeof v === 'boolean') {
          return (
            <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
              v ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 
                  'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
            }`}>
              {v ? 'Yes' : 'No'}
            </span>
          );
        }
        if (typeof v === 'number') {
          // If it looks like a count, format appropriately
          if (key.toLowerCase().includes('count') || key.toLowerCase().includes('imported')) {
            return (
              <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                {v} item{v !== 1 ? 's' : ''}
              </span>
            );
          }
          return v.toString();
        }
        if (typeof v === 'string') {
          // Format dates
          if (key.toLowerCase().includes('date') || /^\d{4}-\d{2}-\d{2}/.test(v)) {
            try {
              return new Date(v).toLocaleDateString('en-AU', { 
                year: 'numeric', 
                month: 'short', 
                day: 'numeric' 
              });
            } catch {
              return v;
            }
          }
          // Format sync types
          if (key.toLowerCase().includes('synctype') || key.toLowerCase().includes('type')) {
            return (
              <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                {v.charAt(0).toUpperCase() + v.slice(1)}
              </span>
            );
          }
          return v;
        }
        if (typeof v === 'object' && v !== null) {
          // For objects, show key-value pairs
          return (
            <div className="ml-2 space-y-1">
              {Object.entries(v).map(([subKey, subValue]) => (
                <div key={subKey} className="text-xs">
                  <span className="font-medium">{formatKey(subKey)}:</span>{' '}
                  <span className="text-gray-600 dark:text-gray-400">{String(subValue)}</span>
                </div>
              ))}
            </div>
          );
        }
        return v;
      };
      
      const formattedValue = formatValue(value);
      
      return (
        <div key={key} className="flex items-start justify-between text-xs">
          <span className="font-medium text-gray-700 dark:text-gray-300 mr-2">
            {formatKey(key)}:
          </span>
          <span className="text-gray-600 dark:text-gray-400 text-right flex-1">
            {formattedValue}
          </span>
        </div>
      );
    }).filter(Boolean);
  };

  // Enhance the subject line for better readability
  const getEnhancedSubject = (activity: Activity) => {
    const { type, subject, metadata } = activity;
    
    // For sync activities, make them more user-friendly
    if (type.includes('sync_completed')) {
      const syncType = metadata?.syncType || 'data';
      const count = metadata?.importedCount || 0;
      const source = activity.source;
      
      return `${source.charAt(0).toUpperCase() + source.slice(1)} sync completed - ${count} ${syncType} item${count !== 1 ? 's' : ''} imported`;
    }
    
    if (type.includes('sync_started')) {
      const syncType = metadata?.syncType || 'data';
      const source = activity.source;
      
      return `${source.charAt(0).toUpperCase() + source.slice(1)} ${syncType} sync started`;
    }
    
    if (type.includes('sync_failed')) {
      const syncType = metadata?.syncType || 'data';
      const source = activity.source;
      
      return `${source.charAt(0).toUpperCase() + source.slice(1)} ${syncType} sync failed`;
    }
    
    // For other activities, use the existing subject but capitalize properly
    return subject;
  };

  // Check if activity has expandable content
  const hasExpandableContent = activity.description || activity.metadata;

  return (
    <div className="relative">
      {/* Connector line */}
      {showConnector && (
        <div className="absolute top-10 left-4 w-0.5 h-full bg-gray-200 dark:bg-gray-700" />
      )}

      {/* Activity content */}
      <div className="relative flex items-start space-x-3">
        {/* Activity icon */}
        <div className="flex-shrink-0">
          {getActivityIcon(activity.type, activity.source)}
        </div>

        {/* Activity details */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              {/* Subject and metadata */}
              <div className="flex items-center space-x-2">
                <p className={`text-sm font-medium ${getImportanceColor(activity.importance)}`}>
                  {getEnhancedSubject(activity)}
                </p>
                {activity.importance === 'high' && (
                  <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                    High Priority
                  </span>
                )}
                {!activity.isRead && (
                  <span className="inline-block w-2 h-2 bg-blue-500 rounded-full" />
                )}
              </div>

              {/* Description preview */}
              {activity.description && (
                <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                  {activity.description}
                </p>
              )}

              {/* Source and user info */}
              <div className="mt-2 flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                <span className="capitalize">
                  {activity.source === 'hubspot' ? 'HubSpot' : 
                   activity.source === 'xero' ? 'Xero' : 
                   activity.source === 'harvest' ? 'Harvest' :
                   activity.source}
                </span>
                {activity.createdBy !== 'system' && activity.createdBy !== 'unknown-user' && (
                  <span>by {activity.createdBy}</span>
                )}
                <span>{formatTime(activity.createdAt)}</span>
              </div>

              {/* Expand/collapse button */}
              {hasExpandableContent && (
                <button
                  onClick={() => setIsExpanded(!isExpanded)}
                  className="mt-2 text-xs text-blue-600 hover:text-blue-500 dark:text-blue-400"
                >
                  {isExpanded ? 'Show less' : 'Show more'}
                </button>
              )}

              {/* Expanded content */}
              {isExpanded && (
                <div className="mt-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                  {activity.description && (
                    <div className="mb-3">
                      <h4 className="text-xs font-medium text-gray-900 dark:text-white mb-1">
                        Description
                      </h4>
                      <p className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-line">
                        {activity.description}
                      </p>
                    </div>
                  )}

                  {/* Activity timing info */}
                  <div className="mb-3">
                    <h4 className="text-xs font-medium text-gray-900 dark:text-white mb-1">
                      Activity Info
                    </h4>
                    <div className="space-y-1">
                      <div className="flex items-start justify-between text-xs">
                        <span className="font-medium text-gray-700 dark:text-gray-300 mr-2">
                          Occurred:
                        </span>
                        <span className="text-gray-600 dark:text-gray-400 text-right flex-1">
                          {new Date(activity.createdAt).toLocaleString('en-AU', {
                            year: 'numeric',
                            month: 'short',
                            day: 'numeric',
                            hour: 'numeric',
                            minute: '2-digit'
                          })}
                        </span>
                      </div>
                      <div className="flex items-start justify-between text-xs">
                        <span className="font-medium text-gray-700 dark:text-gray-300 mr-2">
                          Source:
                        </span>
                        <span className="text-gray-600 dark:text-gray-400 text-right flex-1">
                          {activity.source === 'hubspot' ? 'HubSpot' : 
                           activity.source === 'xero' ? 'Xero' : 
                           activity.source === 'harvest' ? 'Harvest' :
                           activity.source}
                        </span>
                      </div>
                      {activity.createdBy !== 'system' && activity.createdBy !== 'unknown-user' && (
                        <div className="flex items-start justify-between text-xs">
                          <span className="font-medium text-gray-700 dark:text-gray-300 mr-2">
                            Created By:
                          </span>
                          <span className="text-gray-600 dark:text-gray-400 text-right flex-1">
                            {activity.createdBy}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>

                  {activity.metadata && Object.keys(activity.metadata).length > 0 && (
                    <div>
                      <h4 className="text-xs font-medium text-gray-900 dark:text-white mb-1">
                        Additional Details
                      </h4>
                      <div className="space-y-1">
                        {renderMetadata(activity.metadata).length > 0 ? 
                          renderMetadata(activity.metadata) :
                          <div className="text-xs text-gray-500 dark:text-gray-400 italic">
                            All relevant details are shown in the activity summary above
                          </div>
                        }
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Timestamp */}
            <div className="flex-shrink-0 text-xs text-gray-500 dark:text-gray-400">
              {formatTime(activity.createdAt)}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ActivityItem;
