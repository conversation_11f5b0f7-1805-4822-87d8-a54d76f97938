-- Add projects as first-class entities for knowledge graph
-- This migration creates a projects table and relationships
-- Safe for production: Uses IF NOT EXISTS for all operations

-- Migration will be tracked automatically by simple-migrate.js
-- No manual INSERT needed here

-- Create projects table (safe - IF NOT EXISTS)
CREATE TABLE IF NOT EXISTS project (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    status TEXT NOT NULL DEFAULT 'active', -- active, on_hold, completed, cancelled
    project_type TEXT, -- implementation, support, consulting, development
    
    -- Timing
    start_date TEXT,
    end_date TEXT,
    
    -- Financial
    budget REAL,
    spent REAL,
    currency TEXT DEFAULT 'AUD',
    
    -- External IDs
    harvest_project_id TEXT UNIQUE,
    
    -- Relationships
    company_id TEXT NOT NULL,
    deal_id TEXT, -- Optional link to originating deal
    
    -- Metadata
    tags TEXT, -- JSON array of tags
    custom_fields TEXT, -- JSON object
    
    -- Audit
    created_at TEXT NOT NULL DEFAULT (datetime('now')),
    updated_at TEXT NOT NULL DEFAULT (datetime('now')),
    created_by TEXT,
    updated_by TEXT,
    deleted_at TEXT,
    
    FOREIGN KEY (company_id) REFERENCES company(id) ON DELETE CASCADE,
    FOREIGN KEY (deal_id) REFERENCES deal(id) ON DELETE SET NULL
);

-- Create indexes for projects
CREATE INDEX IF NOT EXISTS idx_project_status ON project(status);
CREATE INDEX IF NOT EXISTS idx_project_company_id ON project(company_id);
CREATE INDEX IF NOT EXISTS idx_project_deal_id ON project(deal_id);
CREATE INDEX IF NOT EXISTS idx_project_harvest_project_id ON project(harvest_project_id);
CREATE INDEX IF NOT EXISTS idx_project_start_date ON project(start_date);
CREATE INDEX IF NOT EXISTS idx_project_end_date ON project(end_date);
CREATE INDEX IF NOT EXISTS idx_project_deleted_at ON project(deleted_at);

-- Create project_contact junction table for project team members
CREATE TABLE IF NOT EXISTS project_contact (
    project_id TEXT NOT NULL,
    contact_id TEXT NOT NULL,
    role TEXT NOT NULL, -- project_manager, developer, consultant, stakeholder, etc.
    allocation_percentage REAL, -- 0-100
    start_date TEXT,
    end_date TEXT,
    
    -- Audit
    created_at TEXT NOT NULL DEFAULT (datetime('now')),
    created_by TEXT,
    updated_at TEXT DEFAULT (datetime('now')),
    updated_by TEXT,
    
    PRIMARY KEY (project_id, contact_id),
    FOREIGN KEY (project_id) REFERENCES project(id) ON DELETE CASCADE,
    FOREIGN KEY (contact_id) REFERENCES contact(id) ON DELETE CASCADE
);

-- Create indexes for project_contact
CREATE INDEX IF NOT EXISTS idx_project_contact_project_id ON project_contact(project_id);
CREATE INDEX IF NOT EXISTS idx_project_contact_contact_id ON project_contact(contact_id);
CREATE INDEX IF NOT EXISTS idx_project_contact_role ON project_contact(role);

-- Create project_dependency table for project relationships
CREATE TABLE IF NOT EXISTS project_dependency (
    id TEXT PRIMARY KEY,
    predecessor_project_id TEXT NOT NULL,
    successor_project_id TEXT NOT NULL,
    dependency_type TEXT NOT NULL, -- blocks, requires, related_to
    lag_days INTEGER DEFAULT 0,
    
    -- Audit
    created_at TEXT NOT NULL DEFAULT (datetime('now')),
    created_by TEXT,
    
    FOREIGN KEY (predecessor_project_id) REFERENCES project(id) ON DELETE CASCADE,
    FOREIGN KEY (successor_project_id) REFERENCES project(id) ON DELETE CASCADE,
    UNIQUE(predecessor_project_id, successor_project_id)
);

-- Create indexes for project_dependency
CREATE INDEX IF NOT EXISTS idx_project_dependency_predecessor ON project_dependency(predecessor_project_id);
CREATE INDEX IF NOT EXISTS idx_project_dependency_successor ON project_dependency(successor_project_id);