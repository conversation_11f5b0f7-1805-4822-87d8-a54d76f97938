/* Modern Design System - 2025 UI/UX Enhancements */

/* === MODERN COLOR PALETTE === */
:root {
  /* Primary - Modern Blue */
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-200: #bfdbfe;
  --color-primary-300: #93c5fd;
  --color-primary-400: #60a5fa;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-800: #1e40af;
  --color-primary-900: #1e3a8a;
  --color-primary-950: #172554;

  /* Success - Softer Green */
  --color-success-50: #f0fdf4;
  --color-success-100: #dcfce7;
  --color-success-200: #bbf7d0;
  --color-success-300: #86efac;
  --color-success-400: #4ade80;
  --color-success-500: #10b981;
  --color-success-600: #059669;
  --color-success-700: #047857;
  --color-success-800: #065f46;
  --color-success-900: #064e3b;

  /* Warning - Warmer Amber */
  --color-warning-50: #fffbeb;
  --color-warning-100: #fef3c7;
  --color-warning-200: #fde68a;
  --color-warning-300: #fcd34d;
  --color-warning-400: #fbbf24;
  --color-warning-500: #f59e0b;
  --color-warning-600: #d97706;
  --color-warning-700: #b45309;
  --color-warning-800: #92400e;
  --color-warning-900: #78350f;

  /* Error - Accessible Red */
  --color-error-50: #fef2f2;
  --color-error-100: #fee2e2;
  --color-error-200: #fecaca;
  --color-error-300: #fca5a5;
  --color-error-400: #f87171;
  --color-error-500: #ef4444;
  --color-error-600: #dc2626;
  --color-error-700: #b91c1c;
  --color-error-800: #991b1b;
  --color-error-900: #7f1d1d;

  /* Neutral - Refined Grays */
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;
  --color-gray-950: #030712;

  /* Shadows - Modern Layered */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

  /* Transitions - Smooth Spring */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-base: 200ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-spring: 500ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* === MODERN BUTTON STYLES === */
@layer components {
  /* Base button with modern styling */
  .btn-modern {
    @apply relative inline-flex items-center justify-center px-6 py-3 text-sm font-medium rounded-lg;
    @apply transition-all duration-200 ease-out;
    @apply transform-gpu active:scale-95;
    @apply focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.1), transparent);
    box-shadow: var(--shadow-sm);
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
  }

  /* Modern hover effect */
  .btn-modern::before {
    content: '';
    @apply absolute inset-0 rounded-lg opacity-0 transition-opacity duration-200;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.2), transparent);
  }

  .btn-modern:hover::before {
    @apply opacity-100;
  }

  /* Primary button with gradient */
  .btn-modern--primary {
    @apply bg-primary-600 text-white;
    @apply hover:bg-primary-700 active:bg-primary-800;
    @apply focus-visible:ring-primary-500;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  }

  .btn-modern--primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.12), 0 2px 4px rgba(0, 0, 0, 0.24);
  }

  /* Secondary button with subtle style */
  .btn-modern--secondary {
    @apply bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100;
    @apply hover:bg-gray-200 dark:hover:bg-gray-700;
    @apply focus-visible:ring-gray-500;
  }

  /* Ghost button */
  .btn-modern--ghost {
    @apply bg-transparent text-gray-700 dark:text-gray-300;
    @apply hover:bg-gray-100 dark:hover:bg-gray-800;
    box-shadow: none;
  }

  /* Loading state */
  .btn-modern--loading {
    @apply pointer-events-none opacity-70;
  }

  .btn-modern--loading::after {
    content: '';
    @apply absolute inset-0 flex items-center justify-center;
    background: inherit;
    border-radius: inherit;
  }

  /* Mobile-optimized sizes */
  .btn-modern--sm {
    @apply px-4 py-2 text-xs;
    min-height: 36px;
  }

  .btn-modern--md {
    @apply px-6 py-3 text-sm;
    min-height: 44px; /* Touch target */
  }

  .btn-modern--lg {
    @apply px-8 py-4 text-base;
    min-height: 52px;
  }
}

/* === MODERN FORM INPUTS === */
@layer components {
  /* Modern input field */
  .input-modern {
    @apply w-full px-4 py-3 text-gray-900 dark:text-gray-100;
    @apply bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-700;
    @apply rounded-lg transition-all duration-200;
    @apply focus:outline-none focus:border-primary-500 focus:ring-2 focus:ring-primary-500 focus:ring-opacity-20;
    min-height: 44px; /* Touch target */
    font-size: 16px; /* Prevent zoom on iOS */
  }

  /* Floating label effect */
  .input-group {
    @apply relative;
  }

  .input-group .input-modern {
    @apply pt-6 pb-2;
  }

  .input-label {
    @apply absolute left-4 top-3 text-sm text-gray-500 dark:text-gray-400;
    @apply transition-all duration-200 pointer-events-none;
    transform-origin: left center;
  }

  .input-modern:focus ~ .input-label,
  .input-modern:not(:placeholder-shown) ~ .input-label {
    transform: translateY(-1rem) scale(0.85);
    @apply text-primary-600 dark:text-primary-400;
  }

  /* Input states */
  .input-modern--error {
    @apply border-error-500 focus:border-error-500 focus:ring-error-500 focus:ring-opacity-20;
  }

  .input-modern--success {
    @apply border-success-500 focus:border-success-500 focus:ring-success-500 focus:ring-opacity-20;
  }
}

/* === MODERN CARD STYLES === */
@layer components {
  /* Enhanced card with modern depth */
  .card-modern {
    @apply bg-white dark:bg-gray-800 rounded-xl overflow-hidden;
    @apply border border-gray-200 dark:border-gray-700;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.1);
    @apply transition-all duration-300;
    padding: theme("spacing.fluid-md");
  }

  /* Container query variants for card padding */
  @container (min-width: 300px) {
    .card-modern {
      padding: theme("spacing.fluid-lg");
    }
  }

  @container (min-width: 500px) {
    .card-modern {
      padding: theme("spacing.fluid-xl");
    }
  }

  /* Interactive card */
  .card-modern--interactive {
    @apply cursor-pointer;
  }

  .card-modern--interactive:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.14);
  }

  .card-modern--interactive:active {
    transform: translateY(0);
  }

  /* Glass morphism card */
  .card-glass {
    @apply bg-white/80 dark:bg-gray-800/80 backdrop-blur-md;
    @apply border border-gray-200/50 dark:border-gray-700/50;
    @apply rounded-xl;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  }
}

/* === MODERN ANIMATIONS === */
@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-down {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scale-in {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Animation utilities */
.animate-slide-up {
  animation: slide-up 0.3s ease-out;
}

.animate-slide-down {
  animation: slide-down 0.3s ease-out;
}

.animate-fade-in {
  animation: fade-in 0.3s ease-out;
}

.animate-scale-in {
  animation: scale-in 0.3s ease-out;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* === MICRO-INTERACTIONS === */
@layer utilities {
  /* Haptic feedback simulation */
  .haptic-light {
    @apply active:animate-pulse;
  }

  .haptic-medium {
    @apply active:scale-95 transition-transform duration-75;
  }

  .haptic-heavy {
    @apply active:scale-90 transition-transform duration-100;
  }

  /* Spring animations */
  .spring-bounce {
    transition: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }

  .spring-smooth {
    transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  }

  /* Touch optimizations */
  .touch-manipulation {
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  /* Momentum scrolling */
  .scroll-smooth {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* Safe areas for modern devices */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-left {
    padding-left: env(safe-area-inset-left);
  }

  .safe-right {
    padding-right: env(safe-area-inset-right);
  }
}

/* === FOCUS STATES === */
@layer components {
  /* Modern focus ring */
  .focus-ring {
    @apply focus:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2;
  }

  /* Smooth focus transition */
  .focus-smooth {
    @apply transition-all duration-200;
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-opacity-30;
  }
}

/* === LOADING STATES === */
@layer components {
  /* Skeleton loader */
  .skeleton {
    @apply bg-gray-200 dark:bg-gray-700 rounded animate-pulse;
  }

  .skeleton-text {
    @apply h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse;
  }

  .skeleton-circle {
    @apply bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse;
  }

  /* Modern spinner */
  .spinner {
    @apply inline-block w-5 h-5 border-2 border-gray-300 dark:border-gray-600;
    @apply border-t-primary-600 dark:border-t-primary-400 rounded-full;
    animation: spin 0.8s linear infinite;
  }
}

/* === TYPOGRAPHY ENHANCEMENTS === */
@layer base {
  /* Add Inter font */
  @font-face {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 100 900;
    font-display: swap;
    src: url('https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfAZ9hiA.woff2') format('woff2');
  }

  /* Modern text styles */
  .text-display {
    @apply text-4xl md:text-5xl font-bold tracking-tight;
  }

  .text-headline {
    @apply text-2xl md:text-3xl font-semibold tracking-tight;
  }

  .text-title {
    @apply text-xl md:text-2xl font-semibold;
  }

  .text-body {
    @apply text-base leading-relaxed;
  }

  .text-caption {
    @apply text-sm text-gray-600 dark:text-gray-400;
  }
}

/* === 8. Mobile Floating Action Button (FAB) === */
@layer components {
  /* Container for FAB and sub-actions */
  .mobile-fab-container {
    @apply transition-all duration-300 ease-out;
  }

  .mobile-fab-container button {
    /* Apply haptic feedback on touch */
    @apply touch-manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  /* FAB shadow elevation */
  .fab-elevated {
    box-shadow: 
      0 3px 5px -1px rgba(0, 0, 0, 0.2),
      0 6px 10px 0 rgba(0, 0, 0, 0.14),
      0 1px 18px 0 rgba(0, 0, 0, 0.12);
  }

  .fab-elevated:hover {
    box-shadow: 
      0 5px 5px -3px rgba(0, 0, 0, 0.2),
      0 8px 10px 1px rgba(0, 0, 0, 0.14),
      0 3px 14px 2px rgba(0, 0, 0, 0.12);
  }

  /* FAB rotation animation */
  @keyframes fab-rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(45deg); }
  }

  /* Mobile navigation scroll indicator */
  .nav-scroll-indicator {
    @apply absolute top-0 bottom-0 w-6 pointer-events-none z-10;
    background: linear-gradient(to right, theme("colors.white"), transparent);
  }

  .nav-scroll-indicator--right {
    @apply right-0;
    background: linear-gradient(to left, theme("colors.white"), transparent);
  }

  .dark .nav-scroll-indicator {
    background: linear-gradient(to right, theme("colors.gray.800"), transparent);
  }

  .dark .nav-scroll-indicator--right {
    background: linear-gradient(to left, theme("colors.gray.800"), transparent);
  }
}