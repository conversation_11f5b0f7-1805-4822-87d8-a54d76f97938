-- Migration: 008_tender_qualification
-- Purpose: Add tender qualification workflow for processing TendersWA emails
-- Date: 2025-01-06

-- Create tender table for tracking tender opportunities
CREATE TABLE IF NOT EXISTS tender (
  id TEXT PRIMARY KEY,
  
  -- Tender Information (from email)
  request_no TEXT UNIQUE NOT NULL,
  status TEXT DEFAULT 'Current', -- 'Current', 'Closed', etc.
  type TEXT DEFAULT 'Tender',
  summary TEXT NOT NULL,
  issued_by TEXT NOT NULL,
  unspsc TEXT, -- Universal Standard Products and Services Code
  closing_date TEXT NOT NULL,
  
  -- Email Source
  source_email TEXT, -- Original email content
  tender_url TEXT, -- Link to tender details
  additional_info TEXT, -- Additional information from web scraping
  
  -- Qualification Workflow
  qualification_status TEXT DEFAULT 'new' CHECK(qualification_status IN ('new', 'reviewing', 'not_interested', 'interested')),
  qualification_reason TEXT, -- Why qualified in/out
  qualified_by TEXT,
  qualified_at TEXT,
  
  -- Relationships
  company_id TEXT, -- Auto-linked based on issued_by
  deal_id TEXT, -- Created when moved to 'interested'
  
  -- Metadata
  tags TEXT, -- JSON array of tags
  notes TEXT,
  
  -- <PERSON><PERSON>
  created_at TEXT NOT NULL DEFAULT (datetime('now')),
  updated_at TEXT NOT NULL DEFAULT (datetime('now')),
  created_by TEXT DEFAULT 'email_ingestion',
  updated_by TEXT,
  deleted_at TEXT, -- Soft delete
  
  FOREIGN KEY (company_id) REFERENCES company(id) ON DELETE SET NULL,
  FOREIGN KEY (deal_id) REFERENCES deal(id) ON DELETE SET NULL
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_tender_request_no ON tender(request_no);
CREATE INDEX IF NOT EXISTS idx_tender_qualification_status ON tender(qualification_status);
CREATE INDEX IF NOT EXISTS idx_tender_closing_date ON tender(closing_date);
CREATE INDEX IF NOT EXISTS idx_tender_company_id ON tender(company_id);
CREATE INDEX IF NOT EXISTS idx_tender_deal_id ON tender(deal_id);
CREATE INDEX IF NOT EXISTS idx_tender_issued_by ON tender(issued_by);
CREATE INDEX IF NOT EXISTS idx_tender_created_at ON tender(created_at);
CREATE INDEX IF NOT EXISTS idx_tender_deleted_at ON tender(deleted_at);

-- Add tender-related activity types to support activity tracking
-- This is a comment for documentation, actual activity types are handled in code
-- Activity types: tender_received, tender_qualified, tender_disqualified, tender_converted