-- Rollback migration for 004_projects_table
-- This safely removes project-related tables if needed

-- Drop indexes first (if they exist)
DROP INDEX IF EXISTS idx_project_dependency_successor;
DROP INDEX IF EXISTS idx_project_dependency_predecessor;
DROP INDEX IF EXISTS idx_project_contact_role;
DROP INDEX IF EXISTS idx_project_contact_contact_id;
DROP INDEX IF EXISTS idx_project_contact_project_id;
DROP INDEX IF EXISTS idx_project_deleted_at;
DROP INDEX IF EXISTS idx_project_harvest_project_id;
DROP INDEX IF EXISTS idx_project_deal_id;
DROP INDEX IF EXISTS idx_project_company_id;
DROP INDEX IF EXISTS idx_project_status;

-- Drop tables in reverse order of dependencies
DROP TABLE IF EXISTS project_dependency;
DROP TABLE IF EXISTS project_contact;
DROP TABLE IF EXISTS project;

-- Remove migration record
DELETE FROM migrations WHERE id = '004_projects_table';