-- Migration: 007_knowledge_graph_optimization.sql
-- Purpose: Add indexes to optimize knowledge graph queries
-- Date: January 2025

-- Composite indexes for entity filtering (name + deleted_at)
CREATE INDEX IF NOT EXISTS idx_company_name_deleted ON company(name, deleted_at);
CREATE INDEX IF NOT EXISTS idx_contact_full_name_deleted ON contact(first_name, last_name, deleted_at);
CREATE INDEX IF NOT EXISTS idx_deal_name_stage_deleted ON deal(name, stage, deleted_at);
CREATE INDEX IF NOT EXISTS idx_project_name_status_deleted ON project(name, status, deleted_at);

-- Search optimization indexes
-- These support the LIKE queries in the knowledge graph repository
CREATE INDEX IF NOT EXISTS idx_company_description ON company(description);
CREATE INDEX IF NOT EXISTS idx_contact_email ON contact(email) WHERE email IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_deal_description ON deal(description);

-- Relationship query optimization
-- These help with the node degree calculations and relationship filtering
-- Note: Relationship tables don't have deleted_at columns, only the main entity tables do
CREATE INDEX IF NOT EXISTS idx_contact_company_composite ON contact_company(contact_id, company_id);
CREATE INDEX IF NOT EXISTS idx_contact_role_composite ON contact_role(contact_id, deal_id);
CREATE INDEX IF NOT EXISTS idx_deal_estimate_composite ON deal_estimate(deal_id, estimate_id);
CREATE INDEX IF NOT EXISTS idx_company_relationship_composite ON company_relationship(parent_company_id, child_company_id);
CREATE INDEX IF NOT EXISTS idx_contact_relationships_composite ON contact_relationships(source_contact_id, target_contact_id);
CREATE INDEX IF NOT EXISTS idx_project_contact_composite ON project_contact(project_id, contact_id);
CREATE INDEX IF NOT EXISTS idx_project_dependency_composite ON project_dependency(predecessor_project_id, successor_project_id);

-- Entity counting indexes for pagination
CREATE INDEX IF NOT EXISTS idx_company_deleted_at ON company(deleted_at);
CREATE INDEX IF NOT EXISTS idx_contact_deleted_at ON contact(deleted_at);
CREATE INDEX IF NOT EXISTS idx_deal_deleted_at ON deal(deleted_at);
CREATE INDEX IF NOT EXISTS idx_project_deleted_at ON project(deleted_at);
CREATE INDEX IF NOT EXISTS idx_estimate_deleted_at ON estimate(deleted_at);

-- Migration will be tracked automatically by simple-migrate.js
-- No manual INSERT needed here