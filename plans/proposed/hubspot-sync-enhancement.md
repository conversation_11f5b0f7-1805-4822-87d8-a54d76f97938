# HubSpot Sync Enhancement Plan

## Overview
Enhance the HubSpot integration to provide separate quick and full sync options, with the ability to schedule full syncs during off-peak hours.

## Problem Statement
- Current HubSpot sync imports everything at once, including slow operations (notes/activities/associations)
- Users experience long wait times when they only need core entity data
- No way to schedule syncs for off-peak hours

## Proposed Solution

### 1. Two-Tier Sync System

#### Quick Sync (Core Entities)
- **Imports**: Companies → Deals → Contacts
- **Time**: ~30 seconds to 2 minutes
- **Use Case**: Daily updates, immediate needs

#### Full Sync (Everything)
- **Imports**: Companies → Deals → Contacts → Notes/Activities → Associations
- **Time**: 5-15+ minutes depending on data volume
- **Use Case**: Comprehensive sync, scheduled runs

### 2. UI Enhancement Design

#### Current State
```
[Import All Data] - Single blue button
```

#### Proposed State
```
┌─────────────────────────────────────────────────┐
│ Sync Options                                    │
│                                                 │
│ ┌─────────────┐  ┌─────────────┐              │
│ │ Quick Sync  │  │ Full Sync   │              │
│ │   ⚡ Fast   │  │  📊 Complete │              │
│ └─────────────┘  └─────────────┘              │
│                                                 │
│ Last quick sync: 2 hours ago                   │
│ Last full sync: Yesterday at 3:15 AM           │
│                                                 │
│ ⏰ Automated full sync: Daily at 3:00 AM ✓     │
└─────────────────────────────────────────────────┘
```

#### Design Details
- Two primary action buttons side by side
- Icon indicators (⚡ for quick, 📊 for complete)
- Subtle info tooltips on hover explaining what each includes
- Last sync timestamps for each type
- Automated sync toggle with schedule display

### 3. Backend Implementation

#### New API Endpoints

```typescript
// Quick sync endpoint
POST /api/hubspot/import/quick
Response: {
  success: boolean,
  data: {
    totalCount: number,
    results: {
      companies: ImportResult,
      deals: ImportResult,
      contacts: ImportResult
    }
  }
}

// Schedule management
GET /api/hubspot/schedule
POST /api/hubspot/schedule
DELETE /api/hubspot/schedule
```

#### Service Layer Changes

```typescript
// hubspot/index.ts
class HubSpotService {
  // New method for quick sync
  async importQuickWithProgress(): Promise<QuickImportResult> {
    // Import only companies, deals, contacts
    // Skip notes and associations
  }
  
  // Schedule management
  async getSchedule(): Promise<ScheduleConfig | null>
  async setSchedule(config: ScheduleConfig): Promise<boolean>
  async clearSchedule(): Promise<boolean>
}
```

#### Database Schema Addition

```sql
-- New table for sync schedules
CREATE TABLE IF NOT EXISTS sync_schedule (
  id TEXT PRIMARY KEY,
  integration_type TEXT NOT NULL CHECK(integration_type IN ('hubspot', 'harvest', 'xero')),
  sync_type TEXT NOT NULL CHECK(sync_type IN ('quick', 'full')),
  schedule TEXT NOT NULL, -- Cron expression
  enabled INTEGER DEFAULT 1,
  last_run TEXT,
  next_run TEXT,
  created_at TEXT NOT NULL DEFAULT (datetime('now')),
  updated_at TEXT NOT NULL DEFAULT (datetime('now')),
  created_by TEXT,
  updated_by TEXT,
  deleted_at TEXT
);
```

### 4. Scheduling System

#### Technology Choice
- **node-cron**: Lightweight, reliable cron-like scheduler for Node.js
- Runs in-process, no external dependencies
- Supports standard cron expressions

#### Implementation

```typescript
// services/scheduler/hubspot-scheduler.ts
import * as cron from 'node-cron';
import { hubspotService } from '../hubspot';

export class HubSpotScheduler {
  private task: cron.ScheduledTask | null = null;
  
  async initialize() {
    const schedule = await this.getScheduleFromDB();
    if (schedule?.enabled) {
      this.scheduleTask(schedule.cronExpression);
    }
  }
  
  scheduleTask(cronExpression: string = '0 3 * * *') {
    this.task = cron.schedule(cronExpression, async () => {
      console.log('Starting scheduled HubSpot full sync...');
      await hubspotService.importAllWithProgress();
    });
  }
  
  stopTask() {
    if (this.task) {
      this.task.stop();
      this.task = null;
    }
  }
}
```

### 5. Frontend Implementation

#### Updated HubSpotIntegration Component

```tsx
const HubSpotIntegration: React.FC = () => {
  const [syncType, setSyncType] = useState<'quick' | 'full'>('quick');
  const [schedule, setSchedule] = useState<ScheduleConfig | null>(null);
  
  // Separate mutations for quick and full sync
  const quickSyncMutation = useMutation(importQuickFromHubSpot);
  const fullSyncMutation = useMutation(importAllFromHubSpot);
  
  return (
    <div className="sync-options-card">
      <h3>Sync Options</h3>
      
      <div className="sync-buttons">
        <button 
          className="quick-sync-btn"
          onClick={() => quickSyncMutation.mutate()}
        >
          <BoltIcon className="w-5 h-5" />
          <span>Quick Sync</span>
          <span className="subtitle">Companies, Deals, Contacts</span>
        </button>
        
        <button 
          className="full-sync-btn"
          onClick={() => fullSyncMutation.mutate()}
        >
          <ChartBarIcon className="w-5 h-5" />
          <span>Full Sync</span>
          <span className="subtitle">Everything including activities</span>
        </button>
      </div>
      
      <div className="sync-status">
        <p>Last quick sync: {formatRelativeTime(lastQuickSync)}</p>
        <p>Last full sync: {formatRelativeTime(lastFullSync)}</p>
      </div>
      
      <div className="schedule-toggle">
        <label className="flex items-center">
          <input
            type="checkbox"
            checked={schedule?.enabled}
            onChange={handleScheduleToggle}
          />
          <span>Run full sync daily at 3:00 AM</span>
        </label>
      </div>
    </div>
  );
};
```

#### CSS Enhancements

```css
.sync-options-card {
  @apply bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm;
}

.sync-buttons {
  @apply grid grid-cols-2 gap-4 mb-6;
}

.quick-sync-btn, .full-sync-btn {
  @apply flex flex-col items-center justify-center p-6 rounded-lg border-2 
         transition-all duration-200 cursor-pointer;
}

.quick-sync-btn {
  @apply border-blue-200 hover:border-blue-400 hover:bg-blue-50 
         dark:border-blue-800 dark:hover:border-blue-600 dark:hover:bg-blue-900/20;
}

.full-sync-btn {
  @apply border-purple-200 hover:border-purple-400 hover:bg-purple-50
         dark:border-purple-800 dark:hover:border-purple-600 dark:hover:bg-purple-900/20;
}

.sync-status {
  @apply text-sm text-gray-600 dark:text-gray-400 space-y-1 mb-4;
}

.schedule-toggle {
  @apply pt-4 border-t border-gray-200 dark:border-gray-700;
}
```

### 6. Risk Mitigation

#### Data Integrity
- Quick sync only adds/updates, never deletes
- Full sync can run anytime to catch up on notes/associations
- Import order (Companies → Deals → Contacts) always preserved

#### Performance
- Quick sync reduces load on HubSpot API
- Scheduled syncs run during off-peak hours
- Progress tracking shows real-time status

#### User Experience
- Clear labeling of sync options
- Visual feedback during sync
- Sync history shows what type was run

#### Error Handling
- Failed scheduled syncs are logged
- Email notifications for failed scheduled syncs (future enhancement)
- Manual sync always available as fallback

### 7. Implementation Steps

#### Phase 1: Backend Quick Sync (Day 1-2)
1. Create `/api/hubspot/import/quick` endpoint
2. Add `importQuickWithProgress()` to HubSpot service
3. Update import recording to track sync type
4. Test with existing progress tracker

#### Phase 2: Frontend UI Enhancement (Day 2-3)
1. Update HubSpotIntegration component with two buttons
2. Add sync type to import history display
3. Implement last sync timestamps
4. Add tooltips and help text

#### Phase 3: Scheduling System (Day 3-4)
1. Create sync_schedule table
2. Implement HubSpotScheduler service
3. Add schedule API endpoints
4. Initialize scheduler on app startup

#### Phase 4: Frontend Scheduling UI (Day 4-5)
1. Add schedule toggle to UI
2. Display next scheduled sync time
3. Add schedule management API calls
4. Test end-to-end scheduling

#### Phase 5: Testing & Documentation (Day 5)
1. Test all sync scenarios
2. Verify scheduled syncs work correctly
3. Update user documentation
4. Add to CLAUDE.md

### 8. Future Enhancements

1. **Custom Schedules**: Allow users to set custom cron expressions
2. **Selective Sync**: Choose which entity types to sync
3. **Email Notifications**: Alert on sync failures or completion
4. **Sync Queue**: Queue multiple sync requests
5. **Incremental Sync**: Only sync changed records

### 9. Success Metrics

- **Quick Sync Time**: < 2 minutes for typical dataset
- **User Adoption**: 80% of syncs use quick sync
- **Schedule Reliability**: 99% of scheduled syncs complete successfully
- **API Rate Limit**: Stay well within HubSpot limits

### 10. Notes

- Keep existing "Import All Data" functionality for backward compatibility
- Scheduled syncs only run if application is running (consider external scheduler for production)
- Monitor HubSpot API usage to ensure we stay within limits
- Consider adding sync type filter to import history view