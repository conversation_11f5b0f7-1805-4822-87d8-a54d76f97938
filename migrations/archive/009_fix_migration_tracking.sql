-- Fix migration tracking for migration 004 and 007
-- This ensures the migration system recognizes migrations correctly

-- Remove any incorrect entries for migration 004
DELETE FROM migrations WHERE id = '004_projects_table';

-- Insert the correct entry if it doesn't exist
INSERT OR IGNORE INTO migrations (id, name, batch) 
VALUES ('004', '004_projects_table', 4);

-- Also clean up any duplicate migration 007 entries (now renamed to 011)
DELETE FROM migrations WHERE id = '007_knowledge_graph_optimization';
DELETE FROM migrations WHERE id = '007';

-- The simple-migrate.js will track migrations by their numeric prefix